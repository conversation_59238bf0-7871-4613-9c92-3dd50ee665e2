#ifndef BSP_SYSTEM_H
#define BSP_SYSTEM_H

#define sampling_points 1024
#define SCREEN_WIDTH 320
#define SCREEN_HEIGHT 115  // 优化波形显示高度，避免与文字区域重叠
#define SAMPLING_RATE  1333333  // ������Ϊ 1.33MHz


#include "stdio.h"
#include "stdarg.h"
#include "string.h"
#include <stdlib.h>
#include <math.h>
#include "arm_math.h"   // ���� CMSIS-DSP ��
#include "arm_const_structs.h"


#include "rtc.h"
#include "lcd.h"
#include "main.h"
#include "usart.h"
#include "system.h"
#include "filter.h"
#include "scheduler.h"
#include "led_app.h"
#include "key_app.h"
#include "lcd_app.h"
#include "uart_app.h"
#include "adc_app.h"
#include "rtc_app.h"
#include "tim_app.h"
#include "ringbuffer.h"
#include "fft_app.h"

extern uint16_t uart_rx_index;//���ջ�������
extern uint32_t uart_rx_ticks;//���ջ���ʱ���
extern uint8_t uart_rx_buffer[128];//���ջ���
extern uint8_t ucLed[8];// LED ״̬����
extern uint8_t uart_rx_dma_buffer[128];//���ջ���
extern uint32_t dma_buff[sampling_points];//DMA���ջ���
extern uint32_t dma_buff_t[sampling_points];//DMA���ջ���
extern float adc_value[sampling_points];//ADC����ֵ����
extern RTC_TimeTypeDef time;//����ʱ��ṹ��
extern RTC_DateTypeDef date;//�������ڽṹ��
extern uint32_t tim_ic_buffer[64];// ����洢���벶��ֵ�Ļ�����
extern uint32_t tim_ic_val;// ���ռ���õ������벶��ֵ
extern uint32_t vpp;//���ֵ
extern uint32_t vpp_old;//֮ǰ��ȡ���ķ��ֵ
extern uint8_t lcd_change_flag;//�ж�lcd���Ƿ���Ҫˢ��
extern uint32_t max_number;//ADC�����������ֵ
extern uint32_t min_number;//ADC����������Сֵ
extern uint8_t adc_change;//�ж�adc�������ź��Ƿ�ı�
extern float32_t adc_input_buffer[sampling_points*2];
extern uint32_t lcd_show_buff[sampling_points];//lcd��ʾ����
extern uint32_t dma_buff_t[sampling_points];//DMA���ջ���
extern float32_t Vpp;//���ֵʵ����
extern float duty;//ռ�ձ�
extern uint8_t lcd_mode;//lcd��ʾģʽ��0��ʾ���Σ�1��ʾƵ��ͼ
extern float32_t fft_output[sampling_points];  // ���������Ϣ����ͨ������������������
extern uint8_t auto_flag;//�Զ�������־��0��ʾδ�����Զ���1��ʾ�����Զ�
extern uint32_t digital_vpp;//���ֵ������
extern uint8_t input_change_flag;//�ж������ź��Ƿ����ı�ı�־
extern uint32_t input_average;//�����źŵ�ƽ��ֵ
extern uint32_t input_average_old;//֮ǰ�����źŵ�ƽ��ֵ

#endif


