#include "calibration.h"

// 全局校准参数变量
calibration_params_t g_calib_params = {
    .duty_gain = 1.0f,          // 默认占空比增益系数
    .duty_offset = 0.0f,        // 默认占空比偏移量
    .freq_gain = 1.0f,          // 默认频率增益系数
    .freq_offset = 0.0f,        // 默认频率偏移量
    .magic_number = CALIBRATION_MAGIC_NUMBER,
    .version = CALIBRATION_VERSION,
    .reserved = 0
};

// 全局校准状态变量
calibration_status_t g_calib_status = {
    .state = CALIB_STATE_NORMAL,
    .step = CALIB_STEP_IDLE,
    .calibration_mode = 0,
    .current_item = 0,
    .standard_value = 0.0f,
    .measured_value = 0.0f,
    .sample_count = 0,
    .max_samples = 10
};

/**
 * @brief 校准系统初始化
 */
void calibration_init(void)
{
    // 尝试从RTC备份寄存器加载校准参数
    if (load_calibration_params() != 0) {
        // 加载失败，使用默认参数
        calibration_reset_params();
        save_calibration_params();
    }
    
    // 初始化校准状态
    g_calib_status.state = CALIB_STATE_NORMAL;
    g_calib_status.step = CALIB_STEP_IDLE;
    g_calib_status.calibration_mode = 0;
    g_calib_status.current_item = 0;
}

/**
 * @brief 校准系统反初始化
 */
void calibration_deinit(void)
{
    // 保存当前校准参数
    save_calibration_params();
    
    // 重置校准状态
    g_calib_status.state = CALIB_STATE_NORMAL;
    g_calib_status.step = CALIB_STEP_IDLE;
    g_calib_status.calibration_mode = 0;
}

/**
 * @brief 重置校准参数为默认值
 * @return 0:成功, 1:失败
 */
uint8_t calibration_reset_params(void)
{
    g_calib_params.duty_gain = 1.0f;
    g_calib_params.duty_offset = 0.0f;
    g_calib_params.freq_gain = 1.0f;
    g_calib_params.freq_offset = 0.0f;
    g_calib_params.magic_number = CALIBRATION_MAGIC_NUMBER;
    g_calib_params.version = CALIBRATION_VERSION;
    g_calib_params.reserved = 0;
    
    return 0;
}

/**
 * @brief 获取校准状态
 * @return 校准状态值
 */
uint8_t calibration_get_status(void)
{
    return (uint8_t)g_calib_status.state;
}

/**
 * @brief float转uint32转换函数
 * @param value 要转换的float值
 * @return 转换后的uint32值
 */
uint32_t float_to_uint32(float value)
{
    union {
        float f;
        uint32_t u;
    } converter;
    
    converter.f = value;
    return converter.u;
}

/**
 * @brief uint32转float转换函数
 * @param value 要转换的uint32值
 * @return 转换后的float值
 */
float uint32_to_float(uint32_t value)
{
    union {
        float f;
        uint32_t u;
    } converter;
    
    converter.u = value;
    return converter.f;
}

/**
 * @brief 验证校准参数有效性
 * @return 0:有效, 1:无效
 */
uint8_t validate_calibration_params(void)
{
    // 检查魔数
    if (g_calib_params.magic_number != CALIBRATION_MAGIC_NUMBER) {
        return 1;
    }
    
    // 检查版本
    if (g_calib_params.version != CALIBRATION_VERSION) {
        return 1;
    }
    
    // 检查增益系数范围
    if (g_calib_params.duty_gain < CALIB_GAIN_MIN || 
        g_calib_params.duty_gain > CALIB_GAIN_MAX) {
        return 1;
    }
    
    if (g_calib_params.freq_gain < CALIB_GAIN_MIN || 
        g_calib_params.freq_gain > CALIB_GAIN_MAX) {
        return 1;
    }
    
    // 检查偏移量范围
    if (g_calib_params.duty_offset < CALIB_OFFSET_MIN || 
        g_calib_params.duty_offset > CALIB_OFFSET_MAX) {
        return 1;
    }
    
    if (g_calib_params.freq_offset < CALIB_OFFSET_MIN || 
        g_calib_params.freq_offset > CALIB_OFFSET_MAX) {
        return 1;
    }
    
    return 0;
}

/**
 * @brief 进入校准模式
 */
void enter_calibration_mode(void)
{
    g_calib_status.calibration_mode = 1;
    g_calib_status.state = CALIB_STATE_DUTY;  // 默认从占空比校准开始
    g_calib_status.step = CALIB_STEP_INPUT_STD;
    g_calib_status.current_item = 0;
}

/**
 * @brief 退出校准模式
 */
void exit_calibration_mode(void)
{
    g_calib_status.calibration_mode = 0;
    g_calib_status.state = CALIB_STATE_NORMAL;
    g_calib_status.step = CALIB_STEP_IDLE;
    g_calib_status.current_item = 0;
}

/**
 * @brief 切换校准项目
 */
void switch_calibration_item(void)
{
    if (g_calib_status.calibration_mode) {
        g_calib_status.current_item = 1 - g_calib_status.current_item;
        
        if (g_calib_status.current_item == 0) {
            g_calib_status.state = CALIB_STATE_DUTY;
        } else {
            g_calib_status.state = CALIB_STATE_FREQ;
        }
        
        g_calib_status.step = CALIB_STEP_INPUT_STD;
    }
}

/**
 * @brief 中止校准
 */
void abort_calibration(void)
{
    g_calib_status.step = CALIB_STEP_IDLE;
    g_calib_status.sample_count = 0;
}

// 注意：以下函数将在后续任务中实现完整功能
// 这里提供基础框架以确保编译通过

uint8_t save_calibration_params(void) { return 0; }
uint8_t load_calibration_params(void) { return 0; }
uint8_t calibrate_duty_cycle(float standard_duty) { return 0; }
uint8_t calibrate_frequency(float standard_freq) { return 0; }
float apply_duty_calibration(float raw_duty) { return raw_duty; }
float apply_frequency_calibration(float raw_freq) { return raw_freq; }
uint8_t start_calibration(void) { return 0; }
void display_calibration_info(void) { }
void display_calibration_progress(void) { }
void display_calibration_result(uint8_t result) { }
