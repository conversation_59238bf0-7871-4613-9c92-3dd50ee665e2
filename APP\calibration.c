#include "calibration.h"

// 全局校准参数变量，初始化为默认值（无校准）
calibration_params_t g_calib_params = {
    .duty_gain = 1.0f,          // 默认占空比增益系数
    .duty_offset = 0.0f,        // 默认占空比偏移量
    .freq_gain = 1.0f,          // 默认频率增益系数
    .freq_offset = 0.0f         // 默认频率偏移量
};

/**
 * @brief 校准系统初始化
 */
void calibration_init(void)
{
    // 设置一些常用的校准参数，您可以根据实际测试调整这些值

    // 占空比校准参数 - 根据您的测试结果调整
    g_calib_params.duty_gain = 1.02f;      // 占空比增益校准，稍微增加2%
    g_calib_params.duty_offset = -0.5f;    // 占空比偏移校准，减少0.5%

    // 频率校准参数 - 根据您的测试结果调整
    g_calib_params.freq_gain = 0.998f;     // 频率增益校准，稍微减少0.2%
    g_calib_params.freq_offset = 10.0f;    // 频率偏移校准，增加10Hz
}

/**
 * @brief 应用占空比校准
 * @param raw_duty 原始占空比测量值
 * @return 校准后的占空比值
 */
float apply_duty_calibration(float raw_duty)
{
    float calibrated_duty = raw_duty * g_calib_params.duty_gain + g_calib_params.duty_offset;

    // 限制校准后的值在合理范围内
    if (calibrated_duty < 0.0f) {
        calibrated_duty = 0.0f;
    } else if (calibrated_duty > 100.0f) {
        calibrated_duty = 100.0f;
    }

    return calibrated_duty;
}

/**
 * @brief 应用频率校准
 * @param raw_freq 原始频率测量值
 * @return 校准后的频率值
 */
float apply_frequency_calibration(float raw_freq)
{
    float calibrated_freq = raw_freq * g_calib_params.freq_gain + g_calib_params.freq_offset;

    // 限制校准后的值在合理范围内
    if (calibrated_freq < 0.0f) {
        calibrated_freq = 0.0f;
    }

    return calibrated_freq;
}

/**
 * @brief 设置占空比校准参数
 * @param gain 增益系数
 * @param offset 偏移量
 */
void set_duty_calibration(float gain, float offset)
{
    g_calib_params.duty_gain = gain;
    g_calib_params.duty_offset = offset;
}

/**
 * @brief 设置频率校准参数
 * @param gain 增益系数
 * @param offset 偏移量
 */
void set_freq_calibration(float gain, float offset)
{
    g_calib_params.freq_gain = gain;
    g_calib_params.freq_offset = offset;
}
