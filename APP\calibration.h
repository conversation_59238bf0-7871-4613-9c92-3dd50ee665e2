#ifndef __CALIBRATION_H__
#define __CALIBRATION_H__

#include "bsp_system.h"

// 校准参数结构体
typedef struct {
    float duty_gain;            // 占空比校准增益系数
    float duty_offset;          // 占空比校准偏移量
    float freq_gain;            // 频率校准增益系数
    float freq_offset;          // 频率校准偏移量
} calibration_params_t;

// 全局变量声明
extern calibration_params_t g_calib_params;

// 校准函数声明
void calibration_init(void);                           // 校准系统初始化
float apply_duty_calibration(float raw_duty);          // 应用占空比校准
float apply_frequency_calibration(float raw_freq);     // 应用频率校准
void set_duty_calibration(float gain, float offset);   // 设置占空比校准参数
void set_freq_calibration(float gain, float offset);   // 设置频率校准参数

#endif // __CALIBRATION_H__
