#ifndef __CALIBRATION_H__
#define __CALIBRATION_H__

#include "bsp_system.h"

// 校准系统版本和魔数定义
#define CALIBRATION_MAGIC_NUMBER    0x43414C49  // "CALI"
#define CALIBRATION_VERSION         0x0001      // 版本1.0

// RTC备份寄存器地址映射
#define CALIB_BKP_DUTY_GAIN         RTC_BKP_DR0   // 占空比校准增益系数
#define CALIB_BKP_DUTY_OFFSET       RTC_BKP_DR1   // 占空比校准偏移量
#define CALIB_BKP_FREQ_GAIN         RTC_BKP_DR2   // 频率校准增益系数
#define CALIB_BKP_FREQ_OFFSET       RTC_BKP_DR3   // 频率校准偏移量
#define CALIB_BKP_MAGIC_VERSION     RTC_BKP_DR4   // 魔数和版本信息

// 校准参数范围限制
#define CALIB_GAIN_MIN              0.5f          // 校准增益最小值
#define CALIB_GAIN_MAX              2.0f          // 校准增益最大值
#define CALIB_OFFSET_MIN            -50.0f        // 校准偏移最小值
#define CALIB_OFFSET_MAX            50.0f         // 校准偏移最大值

// 校准状态枚举
typedef enum {
    CALIB_STATE_NORMAL = 0,     // 正常测量模式
    CALIB_STATE_DUTY,           // 占空比校准模式
    CALIB_STATE_FREQ,           // 频率校准模式
    CALIB_STATE_ERROR           // 校准错误状态
} calibration_state_t;

// 校准步骤枚举
typedef enum {
    CALIB_STEP_IDLE = 0,        // 空闲状态
    CALIB_STEP_INPUT_STD,       // 输入标准值
    CALIB_STEP_MEASURING,       // 测量中
    CALIB_STEP_CALCULATING,     // 计算校准系数
    CALIB_STEP_COMPLETED,       // 校准完成
    CALIB_STEP_FAILED           // 校准失败
} calibration_step_t;

// 校准参数结构体
typedef struct {
    float duty_gain;            // 占空比校准增益系数
    float duty_offset;          // 占空比校准偏移量
    float freq_gain;            // 频率校准增益系数
    float freq_offset;          // 频率校准偏移量
    uint32_t magic_number;      // 魔数，用于验证数据有效性
    uint16_t version;           // 校准参数版本
    uint16_t reserved;          // 保留字段
} calibration_params_t;

// 校准系统状态结构体
typedef struct {
    calibration_state_t state;  // 当前校准状态
    calibration_step_t step;    // 当前校准步骤
    uint8_t calibration_mode;   // 校准模式标志
    uint8_t current_item;       // 当前校准项目 (0:占空比, 1:频率)
    float standard_value;       // 标准值
    float measured_value;       // 测量值
    uint8_t sample_count;       // 采样计数
    uint8_t max_samples;        // 最大采样次数
} calibration_status_t;

// 全局变量声明
extern calibration_params_t g_calib_params;    // 全局校准参数
extern calibration_status_t g_calib_status;    // 全局校准状态

// 校准系统核心函数声明
void calibration_init(void);                   // 校准系统初始化
void calibration_deinit(void);                 // 校准系统反初始化
uint8_t calibration_reset_params(void);        // 重置校准参数为默认值
uint8_t calibration_get_status(void);          // 获取校准状态

// 参数存储和读取函数
uint8_t save_calibration_params(void);         // 保存校准参数到RTC备份寄存器
uint8_t load_calibration_params(void);         // 从RTC备份寄存器加载校准参数
uint8_t validate_calibration_params(void);     // 验证校准参数有效性

// 数据转换函数
uint32_t float_to_uint32(float value);         // float转uint32
float uint32_to_float(uint32_t value);         // uint32转float

// 校准算法函数
uint8_t calibrate_duty_cycle(float standard_duty);     // 占空比校准
uint8_t calibrate_frequency(float standard_freq);      // 频率校准
float apply_duty_calibration(float raw_duty);          // 应用占空比校准
float apply_frequency_calibration(float raw_freq);     // 应用频率校准

// 校准模式控制函数
void enter_calibration_mode(void);             // 进入校准模式
void exit_calibration_mode(void);              // 退出校准模式
void switch_calibration_item(void);            // 切换校准项目
uint8_t start_calibration(void);               // 开始校准
void abort_calibration(void);                  // 中止校准

// 校准界面相关函数
void display_calibration_info(void);           // 显示校准信息
void display_calibration_progress(void);       // 显示校准进度
void display_calibration_result(uint8_t result); // 显示校准结果

#endif // __CALIBRATION_H__
