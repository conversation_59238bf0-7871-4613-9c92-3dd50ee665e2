#include "lcd_app.h"

#define WINDOW_SIZE 5

uint8_t lcd_change_flag;//�ж�lcd���Ƿ���Ҫˢ��
uint32_t lcd_show_buff[sampling_points];//lcd��ʾ����
float32_t lcd_frequence;//ͨ��fft�任�õ���Ƶ��
float32_t lcd_frequence_old;//֮ǰ�õ���Ƶ��
uint8_t lcd_mode;//lcd��ʾģʽ��0��ʾ���Σ�1��ʾƵ��ͼ
uint8_t auto_flag;//�Զ�������־��0��ʾδ�����Զ���1��ʾ�����Զ�
float duty;//ʵʱ��ռ�ձ�
float show_duty;//��ʾ��ռ�ձ�
uint8_t first_count;//�״μ���
int target_frequence;//Ƶ�ʳ�������ֵ
int target_duty;//ռ�ձȳ�������ֵ
//float32_t 



/**
 * @brief  ��ʽ���ַ�������ʾ��ָ����LCD���ϡ�
 * 
 * �ú�������һ���кź�һ����ʽ���ַ�����������printf����
 * ��ʽ���ַ����󣬽�����ʾ��LCD��ָ�����ϡ�
 *
 * @param  Line    Ҫ��ʾ�ַ�����LCD�кš�
 * @param  format  ��ʽ���ַ��������Ҫ��ʽ���Ĳ�����
 *
 * �ú����ڲ�ʹ�� `vsprintf` ����ʽ���ַ�����Ȼ��
 * ���� `LCD_DisplayStringLine` ��LCD����ʾ��ʽ������ַ�����
 *
 * ʾ���÷�:
 * @code
 * LcdSprintf(0, "Temperature: %d C", temperature);
 * @endcode
 */
void LcdSprintf(uint8_t Line, char *format,...)
{
    char String[21];  // ���������ڴ洢��ʽ������ַ���
    va_list arg;      // �����б����ڴ洢�ɱ����
    va_start(arg, format);  // ʹ�ø�ʽ���ַ�����ʼ�������б�
    vsprintf(String, format, arg);  // ��ʽ���ַ������洢�ڻ�������
    va_end(arg);  // ���������б�
    LCD_DisplayStringLine(Line,String);  // ��LCD��ָ������ʾ��ʽ������ַ���
}

//ӳ�亯������adc�е�ֵӳ�䵽lcd����
//����˵����value��adc��ֵ   
//in_min,in_max��adc�ķ�Χ����0~4095   
//out_min,out_max��ӳ�䵽��Ļ�ϵķ�Χ����240*320
int map(int value, int in_min, int in_max, int out_min, int out_max)
{
    return (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

//uint16_t *data��ָ��洢���β������ݵĻ��������� ADC ���ݣ���
//uint16_t size�����ݻ������Ĵ�С�������õĲ���������

void DisplayWaveform(uint32_t *data, uint32_t size) {
    // �������ݲ����Ʋ���
    for (int i = 0; i < SCREEN_WIDTH - 1 && i < size - 1; i++) {
        // ӳ�����ݵ���Ļ����
        int y1 = i;
        int x1 = map(data[i], 0, 4095,0, SCREEN_HEIGHT);
        int y2 = i + 1;
        int x2 = map(data[i + 1], 0, 4095,0, SCREEN_HEIGHT);

        // ���Ƶ����֮����߶�
        my_LCD_DrawLine(x1, y1, x2, y2);
    }
}

/* @brief ����Ƶ��ͼ
 * @param fft_data: ָ��FFT������ķ�������
 * @param data_size: FFT���ݵĴ�С
 * @param sampling_rate: �����ʣ�Hz��
 */
void DisplaySpectrum(float32_t *fft_data, uint32_t data_size, uint32_t sampling_rate) 
{
    // ����FFT����ĶԳ��ԣ�����ֻ�����ǰ�벿������
    uint32_t display_size = data_size / 2;
    
    // ����Ƶ�ʷֱ��ʣ�Hz/bin��
    float32_t freq_resolution = (float32_t)sampling_rate / (float32_t)data_size;
    
    // �ҵ�������ֵ���ڹ�һ��
    float32_t max_magnitude = 0.0f;
    for (uint32_t i = 1; i < display_size; i++) 
    {  // ��1��ʼ������ֱ������
        if (fft_data[i] > max_magnitude) 
        {
            max_magnitude = fft_data[i];
        }
    }
    
    // ���û����Ч�źţ�����������
    if (max_magnitude < 0.0001f) 
    {
        max_magnitude = 1.0f;
    }
    
    // ȷ��Ƶ�׵ĺ���ֲ�
    // ÿ��Ƶ��bin��Ӧ��������
    float32_t bins_per_pixel = (float32_t)display_size / (float32_t)SCREEN_WIDTH;
    
    // ����Ƶ��
    for (uint32_t i = 0; i < SCREEN_WIDTH; i++) 
    {
        // ȷ����ǰ���ض�Ӧ��Ƶ��bin����
        uint32_t bin_index = (uint32_t)(i * bins_per_pixel) + 1;  // +1 ����ֱ������
        
        // ȷ����������Ч��Χ��
        if (bin_index >= display_size) 
        {
            bin_index = display_size - 1;
        }
        
        // �����ȹ�һ����ӳ�䵽��Ļ�߶�
        // �������ſ������С�źŵĿɼ���
        float32_t magnitude = fft_data[bin_index] / max_magnitude;
        
        // ��ѡ����������ʹС�źŸ��ɼ���ȡ��ע�������ã�
        // magnitude = log10f(1.0f + 9.0f * magnitude);  // log10(1+9*mag)��[0,1]ӳ�䵽[0,1]
        
        // ӳ�䵽��Ļ�߶�
        int bar_height = map((int)(magnitude * 1000), 0, 1000, 0, SCREEN_HEIGHT);
        
        // ����Ƶ����״ͼ
        my_LCD_DrawLine(0, i, bar_height, i);
        
    }
    
}

float calculate_duty_cycle(uint32_t dma_buff[sampling_points], int max_value, int min_value) {
    int threshold = (max_value + min_value) / 2;  // ������ֵ
    
    int high_count = 0;  // �ߵ�ƽ����
    
    // �������в����㣬����ߵ�ƽ����
    for (int i = 0; i < sampling_points; i++) {
        if (dma_buff[i] > threshold) {
            high_count++;  // �����ǰ�����������ֵ����Ϊ�ߵ�ƽ
        }
    }
    
    // ����ռ�ձ�
    return (float)high_count / sampling_points * 100;
}

void DisplaySimpleAutoAdjustedWaveform(uint32_t *data, uint32_t size) 
{
    // ʹ��ջ�ڴ���Ƕ��ڴ棬�����ڵ�Ƭ�������ж�̬�����ڴ�
    uint32_t minVal = data[0];
    uint32_t maxVal = data[0];
    
    // һ�α����ҳ����ֵ����Сֵ
    for (int i = 1; i < size; i++) {
        if (data[i] < minVal) minVal = data[i];
        if (data[i] > maxVal) maxVal = data[i];
    }
    
    // ��ֹ���������
    if (maxVal == minVal) maxVal = minVal + 1;
    
    // Ԥ���㳣��������ÿ��ѭ���ڵļ���
    uint32_t centerLine = SCREEN_HEIGHT / 2;  // 现在是115/2=57，波形中心线
    uint32_t idealAmplitude = (SCREEN_HEIGHT * 4) / 5; // 使用80%的屏幕高度，现在是92像素
    uint32_t dataDiff = maxVal - minVal;
    uint32_t dataOffset = (maxVal + minVal) / 2;
    
    // ===== Ƶ�ʷ�������� - �������� =====
    // �򵥹��Ʋ���Ƶ�� - ��������
    int zeroCrossings = 0;
    int32_t avgValue = dataOffset; // ʹ�ü���õ�ƽ��ֵ
    int32_t prevDiff = (int32_t)data[0] - avgValue;
    
    for (int i = 1; i < size; i++) {
        int32_t currDiff = (int32_t)data[i] - avgValue;
        // ������㣬ʹ�ó˻�Ϊ���ж�
        if (prevDiff * currDiff <= 0 && (prevDiff != 0 || currDiff != 0)) {
            zeroCrossings++;
        }
        prevDiff = currDiff;
    }
    
    // ����ÿ�����ڵĲ�������
    uint32_t samplesPerCycle = 0;
    if (zeroCrossings > 1) {
        // һ���������������������
        samplesPerCycle = (size * 2) / zeroCrossings;
    } else {
        // �����ⲻ�����ڣ�������������Ϊһ������
        samplesPerCycle = size;
    }
    
    // ������Ѳ����������Ӧ��Ļ����
    // �������������Ļ����ʾ2-3����������
    uint32_t idealCycles = 2; // ����Ļ����ʾ������������
    uint32_t idealSamplesOnScreen = samplesPerCycle * idealCycles;
    
    // ����������������������
    uint32_t sampleStep = 1; // Ĭ�ϲ������κε�
    
    if (samplesPerCycle < (SCREEN_WIDTH / 8)) {
        // Ƶ��̫�ͣ�����̫��������Ҫѹ����ʾ
        // ʹ��С����ȷ����������ϸ�ڣ���ͨ��������ʾ�������
        sampleStep = 1;
    } else if (idealSamplesOnScreen <= SCREEN_WIDTH) {
        // Ƶ�ʺ��ʣ���������
        sampleStep = 1;
    } else {
        // Ƶ��̫�ߣ���Ҫ����һЩ���Խ�����ʾƵ��
        sampleStep = (idealSamplesOnScreen + SCREEN_WIDTH - 1) / SCREEN_WIDTH;
    }
    
    // ===== ���Ʋ��� =====
    // �޸Ļ���ѭ�������ݼ���Ĳ���������Ʋ���
    for (int i = 0; i < SCREEN_WIDTH - 1; i++) {
        // ���㵱ǰ����һ�������������
        uint32_t idx1 = (i * sampleStep) % size;
        uint32_t idx2 = ((i + 1) * sampleStep) % size;
        
        // �Զ���������ӳ�� - ʹ���������㣬���⸡�����
        int y1 = i;
        int x1 = centerLine + (((int)data[idx1] - (int)dataOffset) * (int)idealAmplitude) / (int)dataDiff;
        
        int y2 = i + 1;
        int x2 = centerLine + (((int)data[idx2] - (int)dataOffset) * (int)idealAmplitude) / (int)dataDiff;
        
        // ȷ����������Ļ��Χ��
        if (x1 < 0) x1 = 0;
        else if (x1 >= SCREEN_HEIGHT) x1 = SCREEN_HEIGHT - 1;
        
        if (x2 < 0) x2 = 0;
        else if (x2 >= SCREEN_HEIGHT) x2 = SCREEN_HEIGHT - 1;
        
        // ���Ƶ����֮����߶�
        my_LCD_DrawLine(x1, y1, x2, y2);
    }
}

//��������˵����new_value��������Ƿ����仯�����ݣ�target����׼ֵ��threshold������Ƿ����仯����ֵ
int cusum_detect(int new_value, int target, int threshold)
{
    int deviation = 0;
    static int cumulative_sum = 0;
    // ���㵱ǰֵ��Ŀ��ֵ��ƫ��
    deviation = new_value - target;
    // �����ۻ���
    cumulative_sum += deviation;
    //# �ж��Ƿ񳬳���ֵ
    if (abs(cumulative_sum) > threshold)
    {
        cumulative_sum = 0;//����ƫ���ۼƺ�
        return 1;
    }
    else
        return 0;

}
    
int cusum_detect_duty(int new_value, int target, int threshold)
{
    int deviation = 0;
    static int cumulative_sum = 0;
    // ���㵱ǰֵ��Ŀ��ֵ��ƫ��
    deviation = new_value - target;
    // �����ۻ���
    cumulative_sum += deviation;
    //# �ж��Ƿ񳬳���ֵ
    if (abs(cumulative_sum) > threshold)
    {
        cumulative_sum = 0;//����ƫ���ۼƺ�
        return 1;
    }
    else
        return 0;

}

void lcd_proc(void)
{   
        // 获取原始测量值并应用校准
        float raw_freq = find_peak_frequency();
        lcd_frequence = apply_frequency_calibration(raw_freq);  // 应用频率校准

        if(first_count == 0)
        {
            target_frequence = (int)lcd_frequence;
            //first_count = 1;
        }
        int a = cusum_detect((int)lcd_frequence,target_frequence,20);
        if(a == 1)
        {
            input_change_flag = 0;
            target_frequence = (int)lcd_frequence;
        }

        // 获取原始占空比并应用校准
        float raw_duty = calculate_duty_cycle(dma_buff,max_number,min_number);
        duty = apply_duty_calibration(raw_duty);  // 应用占空比校准

        if(first_count == 0)
        {
            show_duty = duty;
            target_duty = (int)lcd_frequence;
            first_count = 1;
        }
        if(cusum_detect_duty((int)duty,target_duty,3) == 1)
        {
            target_duty = (int)duty;
            show_duty = duty;
        }
//        if(Vpp>=1)
//            LcdSprintf(Line0,"Vpp=%.2fV  ",Vpp);
//        else
//            LcdSprintf(Line0,"Vpp=%.2fmV  ",Vpp*100);
//        if(max_number>=1)
//            LcdSprintf(Line1,"Vmax=%.2fV  ",max_number* 3.3f / 4096);
//        else
//            LcdSprintf(Line1,"Vmax=%.2fmV  ",max_number* 3.3f / 4096 *100);
//        if(min_number>=1)
//            LcdSprintf(Line2,"Vmin=%.2fV  ",min_number* 3.3f / 4096);
//        else
//            LcdSprintf(Line2,"Vmin=%.2fmV  ",min_number* 3.3f / 4096 *100);
        LcdSprintf(Line0,"Duty=%.2f%%  ",show_duty);
        if(lcd_frequence<1000)
            LcdSprintf(Line1,"Fre=%.2fHz  ",lcd_frequence);
        else
            LcdSprintf(Line1,"Fre=%.2fKHz  ",lcd_frequence/1000);
        //LcdSprintf(Line5,"%d   ",a);
    switch(lcd_mode)
    {
        case 0:
            if(input_change_flag == 0)
            {
                if(lcd_change_flag==0)
                {
                //LCD_Clear(Black);
                    if(auto_flag==0)
                    {
                        LCD_ClearRegion(125,0,240,320,Black);
                        DisplayWaveform((uint32_t*)lcd_show_buff, sampling_points);
                        input_change_flag = 1;
                    }
                    else
                    {
                        {
                            LCD_ClearRegion(125,0,240,320,Black);
                            DisplaySimpleAutoAdjustedWaveform((uint32_t*)lcd_show_buff, sampling_points);
                            input_change_flag = 1;
                        }

                    }
                }
            
            //lcd_change_flag = 1;
            }
        break;
        case 1:
            LCD_ClearRegion(130,0,240,320,Black);
            DisplaySpectrum(fft_output,sampling_points,SAMPLING_RATE);
        break;
    }
        
}
