// File: STM32G4xx.dbgconf
// Version: 1.0.0
// Note: refer to STM32G4xx Reference manual (RM0440)
//       refer to STM32G4xx datasheets

// <<< Use Configuration Wizard in Context Menu >>>

// <h> Debug MCU configuration register (DBGMCU_CR)
//   <o.2>  DBG_STANDBY              <i> Debug Standby mode
//   <o.1>  DBG_STOP                 <i> Debug Stop mode
//   <o.0>  DBG_SLEEP                <i> Debug Sleep mode
// </h>
DbgMCU_CR = 0x00000007;

// <h> Debug MCU APB1 freeze register1 (DBGMCU_APB1FZR1)
//                                   <i> Reserved bits must be kept at reset value
//   <o.31> DBG_LPTIM1_STOP          <i> LPTIM1 counter stopped when core is halted
//   <o.30> DBG_I2C3_STOP            <i> I2C3 SMBUS timeout counter stopped when core is halted
//   <o.22> DBG_I2C2_STOP            <i> I2C2 SMBUS timeout counter stopped when core is halted
//   <o.21> DBG_I2C1_STOP            <i> I2C1 SMBUS timeout counter stopped when core is halted
//   <o.12> DBG_IWDG_STOP            <i> Independent watchdog counter stopped when core is halted
//   <o.11> DBG_WWDG_STOP            <i> Window watchdog counter stopped when core is halted
//   <o.10> DBG_RTC_STOP             <i> RTC counter stopped when core is halted
//   <o.5>  DBG_TIM7_STOP            <i> TIM7 counter stopped when core is halted
//   <o.4>  DBG_TIM6_STOP            <i> TIM6 counter stopped when core is halted
//   <o.3>  DBG_TIM5_STOP            <i> TIM5 counter stopped when core is halted
//   <o.2>  DBG_TIM4_STOP            <i> TIM4 counter stopped when core is halted
//   <o.1>  DBG_TIM3_STOP            <i> TIM3 counter stopped when core is halted
//   <o.0>  DBG_TIM2_STOP            <i> TIM2 counter stopped when core is halted
// </h>
DbgMCU_APB1_Fz1 = 0x00000000;

// <h> Debug MCU APB1 freeze register 2 (DBGMCU_APB1FZR2)
//                                   <i> Reserved bits must be kept at reset value
//   <o.1>  DBG_I2C4_STOP            <i> I2C4 SMBUS timeout counter stopped when core is halted
// </h>
DbgMCU_APB1_Fz2 = 0x00000000;

// <h> Debug MCU APB2 freeze register (DBGMCU_APB2FZR)
//                                   <i> Reserved bits must be kept at reset value
//   <o.26> DBG_HRTIM_STOP           <i> HRTIM counter stopped when core is halted
//   <o.20> DBG_TIM20_STOP           <i> TIM20 counter stopped when core is halted
//   <o.18> DBG_TIM17_STOP           <i> TIM17 counter stopped when core is halted
//   <o.17> DBG_TIM16_STOP           <i> TIM16 counter stopped when core is halted
//   <o.16> DBG_TIM15_STOP           <i> TIM15 counter stopped when core is halted
//   <o.13> DBG_TIM8_STOP            <i> TIM8 counter stopped when core is halted
//   <o.11> DBG_TIM1_STOP            <i> TIM1 counter stopped when core is halted
// </h>
DbgMCU_APB2_Fz = 0x00000000;
// </h>

// <<< end of configuration section >>>
