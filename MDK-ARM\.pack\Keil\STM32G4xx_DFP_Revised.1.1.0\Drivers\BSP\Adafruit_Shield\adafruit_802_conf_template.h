/**
  ******************************************************************************
  * @file    adafruit_802_conf_template.h
  * <AUTHOR> Application Team
  * @brief   This file includes the nucleo configuration file that contains definitions for:
  *          - ADC information(GPIOs and configuration) used for Joystick
  *          - SPI information(GPIOs and configuration) used for microSD and LCD available
  *            on Adafruit 1.8" TFT LCD shield (reference ID 802)
  *
  *
  *   There are two ways to use this file:
  *   1.
  *    - Copy stm32_adafruit_conf_template.h to the application and rename it to stm32_adafruit_conf.h
  *    - Fill stm32_adafruit_conf.h with adequate resources definition that can be directly
  *      copied from "Adafruit_Config" folder depending on the used nucleo board.
  *    - Un-comment and update the below lines with correct nucleo files
  *
  *   2.
  *    - Copy the required configuration file from "Adafruit_Config" folder to the
  *      application and rename it to stm32_adafruit_conf.h
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2018 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef ADAFRUIT_802_CONF_H
#define ADAFRUIT_802_CONF_H

#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
/* #include "stm32xxxx_nucleo_conf.h"
   #include "stm32xxxx_nucleo_errno.h"
*/
/** @addtogroup BSP
  * @{
  */

/** @addtogroup ADAFRUIT_802
  * @{
  */

/** @defgroup ADAFRUIT_802_CONFIG Config
  * @{
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* ADAFRUIT_802_CONF_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
