/**
  ******************************************************************************
  * @file    font24.c
  * <AUTHOR> Application Team
  * @brief   This file provides text font24 for STM32xx-EVAL's LCD driver. 
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2018 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "fonts.h"

/** @addtogroup Utilities
  * @{
  */
  
/** @addtogroup STM32_EVAL
  * @{
  */ 

/** @addtogroup Common
  * @{
  */

/** @addtogroup FONTS
  * @brief      This file provides text font24 for STM32xx-EVAL's LCD driver.
  * @{
  */  

/** @defgroup FONTS_Private_Variables
  * @{
  */
const uint8_t Font24_Table [] = 
{
	// @0 ' ' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @72 '!' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x01, 0x00, 0x00, //        #         
	0x01, 0x00, 0x00, //        #         
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @144 '"' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x0E, 0x70, 0x00, //     ###  ###     
	0x0E, 0x70, 0x00, //     ###  ###     
	0x0E, 0x70, 0x00, //     ###  ###     
	0x04, 0x20, 0x00, //      #    #      
	0x04, 0x20, 0x00, //      #    #      
	0x04, 0x20, 0x00, //      #    #      
	0x04, 0x20, 0x00, //      #    #      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @216 '#' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x06, 0x60, 0x00, //      ##  ##      
	0x06, 0x60, 0x00, //      ##  ##      
	0x06, 0x60, 0x00, //      ##  ##      
	0x06, 0x60, 0x00, //      ##  ##      
	0x06, 0x60, 0x00, //      ##  ##      
	0x3F, 0xF8, 0x00, //   ###########    
	0x3F, 0xF8, 0x00, //   ###########    
	0x06, 0x60, 0x00, //      ##  ##      
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x3F, 0xF8, 0x00, //   ###########    
	0x3F, 0xF8, 0x00, //   ###########    
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @288 '$' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x07, 0xB0, 0x00, //      #### ##     
	0x0F, 0xF0, 0x00, //     ########     
	0x18, 0x70, 0x00, //    ##    ###     
	0x18, 0x70, 0x00, //    ##    ###     
	0x1C, 0x00, 0x00, //    ###           
	0x0F, 0x80, 0x00, //     #####        
	0x07, 0xE0, 0x00, //      ######      
	0x00, 0xF0, 0x00, //         ####     
	0x18, 0x30, 0x00, //    ##     ##     
	0x1C, 0x30, 0x00, //    ###    ##     
	0x1C, 0x70, 0x00, //    ###   ###     
	0x1F, 0xE0, 0x00, //    ########      
	0x1B, 0xC0, 0x00, //    ## ####       
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @360 '%' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0x80, 0x00, //      ####        
	0x0F, 0xC0, 0x00, //     ######       
	0x1C, 0xE0, 0x00, //    ###  ###      
	0x18, 0x60, 0x00, //    ##    ##      
	0x18, 0x60, 0x00, //    ##    ##      
	0x1C, 0xE0, 0x00, //    ###  ###      
	0x0F, 0xF8, 0x00, //     #########    
	0x07, 0xE0, 0x00, //      ######      
	0x1F, 0xF0, 0x00, //    #########     
	0x07, 0x38, 0x00, //      ###  ###    
	0x06, 0x18, 0x00, //      ##    ##    
	0x06, 0x18, 0x00, //      ##    ##    
	0x07, 0x38, 0x00, //      ###  ###    
	0x03, 0xF0, 0x00, //       ######     
	0x01, 0xE0, 0x00, //        ####      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @432 '&' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xF0, 0x00, //       ######     
	0x07, 0xF0, 0x00, //      #######     
	0x0C, 0x60, 0x00, //     ##   ##      
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x06, 0x00, 0x00, //      ##          
	0x07, 0x00, 0x00, //      ###         
	0x0F, 0x9C, 0x00, //     #####  ###   
	0x1D, 0xFC, 0x00, //    ### #######   
	0x18, 0xF0, 0x00, //    ##   ####     
	0x18, 0x70, 0x00, //    ##    ###     
	0x0F, 0xFC, 0x00, //     ##########   
	0x07, 0xDC, 0x00, //      ##### ###   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @504 ''' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x01, 0x00, 0x00, //        #         
	0x01, 0x00, 0x00, //        #         
	0x01, 0x00, 0x00, //        #         
	0x01, 0x00, 0x00, //        #         
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @576 '(' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x38, 0x00, //           ###    
	0x00, 0x70, 0x00, //          ###     
	0x00, 0xF0, 0x00, //         ####     
	0x00, 0xE0, 0x00, //         ###      
	0x00, 0xE0, 0x00, //         ###      
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0xC0, 0x00, //        ###       
	0x00, 0xE0, 0x00, //         ###      
	0x00, 0xE0, 0x00, //         ###      
	0x00, 0x70, 0x00, //          ###     
	0x00, 0x70, 0x00, //          ###     
	0x00, 0x38, 0x00, //           ###    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @648 ')' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x18, 0x00, 0x00, //    ##            
	0x1C, 0x00, 0x00, //    ###           
	0x0E, 0x00, 0x00, //     ###          
	0x0E, 0x00, 0x00, //     ###          
	0x07, 0x00, 0x00, //      ###         
	0x07, 0x00, 0x00, //      ###         
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x80, 0x00, //       ###        
	0x07, 0x00, 0x00, //      ###         
	0x07, 0x00, 0x00, //      ###         
	0x0F, 0x00, 0x00, //     ####         
	0x0E, 0x00, 0x00, //     ###          
	0x1C, 0x00, 0x00, //    ###           
	0x18, 0x00, 0x00, //    ##            
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @720 '*' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x1D, 0xB8, 0x00, //    ### ## ###    
	0x1F, 0xF8, 0x00, //    ##########    
	0x07, 0xE0, 0x00, //      ######      
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0xC0, 0x00, //       ####       
	0x06, 0x60, 0x00, //      ##  ##      
	0x06, 0x60, 0x00, //      ##  ##      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @792 '+' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x3F, 0xFC, 0x00, //   ############   
	0x3F, 0xFC, 0x00, //   ############   
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @864 ',' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0xE0, 0x00, //         ###      
	0x00, 0xC0, 0x00, //         ##       
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x03, 0x00, 0x00, //       ##         
	0x03, 0x00, 0x00, //       ##         
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @936 '-' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0xF8, 0x00, //    ##########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1008 '.' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0xC0, 0x00, //       ####       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1080 '/' (17 pixels wide)
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x38, 0x00, //           ###    
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x70, 0x00, //          ###     
	0x00, 0x60, 0x00, //          ##      
	0x00, 0x60, 0x00, //          ##      
	0x00, 0xC0, 0x00, //         ##       
	0x00, 0xC0, 0x00, //         ##       
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x03, 0x00, 0x00, //       ##         
	0x03, 0x00, 0x00, //       ##         
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x0E, 0x00, 0x00, //     ###          
	0x0C, 0x00, 0x00, //     ##           
	0x1C, 0x00, 0x00, //    ###           
	0x18, 0x00, 0x00, //    ##            
	0x18, 0x00, 0x00, //    ##            
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1152 '0' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xC0, 0x00, //       ####       
	0x07, 0xE0, 0x00, //      ######      
	0x0C, 0x30, 0x00, //     ##    ##     
	0x0C, 0x30, 0x00, //     ##    ##     
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x0C, 0x30, 0x00, //     ##    ##     
	0x0C, 0x30, 0x00, //     ##    ##     
	0x07, 0xE0, 0x00, //      ######      
	0x03, 0xC0, 0x00, //       ####       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1224 '1' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x80, 0x00, //         #        
	0x07, 0x80, 0x00, //      ####        
	0x1F, 0x80, 0x00, //    ######        
	0x1D, 0x80, 0x00, //    ### ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x1F, 0xF8, 0x00, //    ##########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1296 '2' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xC0, 0x00, //      #####       
	0x1F, 0xF0, 0x00, //    #########     
	0x38, 0x30, 0x00, //   ###     ##     
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x60, 0x00, //          ##      
	0x01, 0xC0, 0x00, //        ###       
	0x03, 0x80, 0x00, //       ###        
	0x06, 0x00, 0x00, //      ##          
	0x0C, 0x00, 0x00, //     ##           
	0x18, 0x00, 0x00, //    ##            
	0x3F, 0xF8, 0x00, //   ###########    
	0x3F, 0xF8, 0x00, //   ###########    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1368 '3' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xC0, 0x00, //       ####       
	0x0F, 0xE0, 0x00, //     #######      
	0x0C, 0x70, 0x00, //     ##   ###     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x60, 0x00, //          ##      
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0xE0, 0x00, //       #####      
	0x00, 0x70, 0x00, //          ###     
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x1F, 0xF0, 0x00, //    #########     
	0x0F, 0xC0, 0x00, //     ######       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1440 '4' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0xE0, 0x00, //         ###      
	0x01, 0xE0, 0x00, //        ####      
	0x01, 0xE0, 0x00, //        ####      
	0x03, 0x60, 0x00, //       ## ##      
	0x06, 0x60, 0x00, //      ##  ##      
	0x06, 0x60, 0x00, //      ##  ##      
	0x0C, 0x60, 0x00, //     ##   ##      
	0x0C, 0x60, 0x00, //     ##   ##      
	0x18, 0x60, 0x00, //    ##    ##      
	0x30, 0x60, 0x00, //   ##     ##      
	0x3F, 0xF8, 0x00, //   ###########    
	0x3F, 0xF8, 0x00, //   ###########    
	0x00, 0x60, 0x00, //          ##      
	0x03, 0xF8, 0x00, //       #######    
	0x03, 0xF8, 0x00, //       #######    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1512 '5' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0xF0, 0x00, //    #########     
	0x1F, 0xF0, 0x00, //    #########     
	0x18, 0x00, 0x00, //    ##            
	0x18, 0x00, 0x00, //    ##            
	0x18, 0x00, 0x00, //    ##            
	0x1B, 0xC0, 0x00, //    ## ####       
	0x1F, 0xF0, 0x00, //    #########     
	0x1C, 0x30, 0x00, //    ###    ##     
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x30, 0x30, 0x00, //   ##      ##     
	0x3F, 0xF0, 0x00, //   ##########     
	0x0F, 0xC0, 0x00, //     ######       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1584 '6' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0xF8, 0x00, //         #####    
	0x03, 0xF8, 0x00, //       #######    
	0x07, 0x00, 0x00, //      ###         
	0x0E, 0x00, 0x00, //     ###          
	0x0C, 0x00, 0x00, //     ##           
	0x18, 0x00, 0x00, //    ##            
	0x1B, 0xC0, 0x00, //    ## ####       
	0x1F, 0xF0, 0x00, //    #########     
	0x1C, 0x30, 0x00, //    ###    ##     
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x0C, 0x38, 0x00, //     ##    ###    
	0x0F, 0xF0, 0x00, //     ########     
	0x03, 0xE0, 0x00, //       #####      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1656 '7' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0xF8, 0x00, //    ##########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x70, 0x00, //          ###     
	0x00, 0x60, 0x00, //          ##      
	0x00, 0x60, 0x00, //          ##      
	0x00, 0xE0, 0x00, //         ###      
	0x00, 0xC0, 0x00, //         ##       
	0x00, 0xC0, 0x00, //         ##       
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1728 '8' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xE0, 0x00, //      ######      
	0x0F, 0xF0, 0x00, //     ########     
	0x1C, 0x38, 0x00, //    ###    ###    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x0C, 0x30, 0x00, //     ##    ##     
	0x07, 0xE0, 0x00, //      ######      
	0x07, 0xE0, 0x00, //      ######      
	0x0C, 0x30, 0x00, //     ##    ##     
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x1C, 0x38, 0x00, //    ###    ###    
	0x0F, 0xF0, 0x00, //     ########     
	0x07, 0xE0, 0x00, //      ######      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1800 '9' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xC0, 0x00, //      #####       
	0x0F, 0xF0, 0x00, //     ########     
	0x1C, 0x30, 0x00, //    ###    ##     
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x0C, 0x38, 0x00, //     ##    ###    
	0x0F, 0xF8, 0x00, //     #########    
	0x03, 0xD8, 0x00, //       #### ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x70, 0x00, //          ###     
	0x00, 0xE0, 0x00, //         ###      
	0x1F, 0xC0, 0x00, //    #######       
	0x1F, 0x00, 0x00, //    #####         
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1872 ':' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0xC0, 0x00, //       ####       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0xC0, 0x00, //       ####       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @1944 ';' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0xF0, 0x00, //         ####     
	0x00, 0xF0, 0x00, //         ####     
	0x00, 0xF0, 0x00, //         ####     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0xE0, 0x00, //         ###      
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x03, 0x00, 0x00, //       ##         
	0x02, 0x00, 0x00, //       #          
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2016 '<' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x1C, 0x00, //            ###   
	0x00, 0x3C, 0x00, //           ####   
	0x00, 0xF0, 0x00, //         ####     
	0x03, 0xC0, 0x00, //       ####       
	0x0F, 0x00, 0x00, //     ####         
	0x3C, 0x00, 0x00, //   ####           
	0xF0, 0x00, 0x00, // ####             
	0x3C, 0x00, 0x00, //   ####           
	0x0F, 0x00, 0x00, //     ####         
	0x03, 0xC0, 0x00, //       ####       
	0x00, 0xF0, 0x00, //         ####     
	0x00, 0x3C, 0x00, //           ####   
	0x00, 0x1C, 0x00, //            ###   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2088 '=' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7F, 0xFC, 0x00, //  #############   
	0x7F, 0xFC, 0x00, //  #############   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7F, 0xFC, 0x00, //  #############   
	0x7F, 0xFC, 0x00, //  #############   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2160 '>' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x70, 0x00, 0x00, //  ###             
	0x78, 0x00, 0x00, //  ####            
	0x1E, 0x00, 0x00, //    ####          
	0x07, 0x80, 0x00, //      ####        
	0x01, 0xE0, 0x00, //        ####      
	0x00, 0x78, 0x00, //          ####    
	0x00, 0x1E, 0x00, //            ####  
	0x00, 0x78, 0x00, //          ####    
	0x01, 0xE0, 0x00, //        ####      
	0x07, 0x80, 0x00, //      ####        
	0x1E, 0x00, 0x00, //    ####          
	0x78, 0x00, 0x00, //  ####            
	0x70, 0x00, 0x00, //  ###             
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2232 '?' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xC0, 0x00, //      #####       
	0x0F, 0xE0, 0x00, //     #######      
	0x18, 0x70, 0x00, //    ##    ###     
	0x18, 0x30, 0x00, //    ##     ##     
	0x18, 0x30, 0x00, //    ##     ##     
	0x00, 0x70, 0x00, //          ###     
	0x00, 0xE0, 0x00, //         ###      
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0x80, 0x00, //       ###        
	0x03, 0x00, 0x00, //       ##         
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0x00, 0x00, //      ###         
	0x07, 0x00, 0x00, //      ###         
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2304 '@' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xE0, 0x00, //       #####      
	0x07, 0xF0, 0x00, //      #######     
	0x0E, 0x38, 0x00, //     ###   ###    
	0x0C, 0x18, 0x00, //     ##     ##    
	0x18, 0x78, 0x00, //    ##    ####    
	0x18, 0xF8, 0x00, //    ##   #####    
	0x19, 0xD8, 0x00, //    ##  ### ##    
	0x19, 0x98, 0x00, //    ##  ##  ##    
	0x19, 0x98, 0x00, //    ##  ##  ##    
	0x19, 0x98, 0x00, //    ##  ##  ##    
	0x18, 0xF8, 0x00, //    ##   #####    
	0x18, 0x78, 0x00, //    ##    ####    
	0x18, 0x00, 0x00, //    ##            
	0x0C, 0x00, 0x00, //     ##           
	0x0E, 0x18, 0x00, //     ###    ##    
	0x07, 0xF8, 0x00, //      ########    
	0x03, 0xE0, 0x00, //       #####      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2376 'A' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0x80, 0x00, //    ######        
	0x1F, 0xC0, 0x00, //    #######       
	0x01, 0xC0, 0x00, //        ###       
	0x03, 0x60, 0x00, //       ## ##      
	0x03, 0x60, 0x00, //       ## ##      
	0x06, 0x30, 0x00, //      ##   ##     
	0x06, 0x30, 0x00, //      ##   ##     
	0x0C, 0x30, 0x00, //     ##    ##     
	0x0F, 0xF8, 0x00, //     #########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x18, 0x0C, 0x00, //    ##       ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0xFC, 0x7F, 0x00, // ######   ####### 
	0xFC, 0x7F, 0x00, // ######   ####### 
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2448 'B' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7F, 0xE0, 0x00, //  ##########      
	0x7F, 0xF0, 0x00, //  ###########     
	0x18, 0x38, 0x00, //    ##     ###    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x1F, 0xF0, 0x00, //    #########     
	0x1F, 0xF8, 0x00, //    ##########    
	0x18, 0x1C, 0x00, //    ##      ###   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x7F, 0xF8, 0x00, //  ############    
	0x7F, 0xF0, 0x00, //  ###########     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2520 'C' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xEC, 0x00, //       ##### ##   
	0x0F, 0xFC, 0x00, //     ##########   
	0x1C, 0x1C, 0x00, //    ###     ###   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x00, 0x00, //   ##             
	0x30, 0x00, 0x00, //   ##             
	0x30, 0x00, 0x00, //   ##             
	0x30, 0x00, 0x00, //   ##             
	0x30, 0x00, 0x00, //   ##             
	0x18, 0x0C, 0x00, //    ##       ##   
	0x1C, 0x1C, 0x00, //    ###     ###   
	0x0F, 0xF8, 0x00, //     #########    
	0x03, 0xF0, 0x00, //       ######     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2592 'D' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7F, 0xC0, 0x00, //  #########       
	0x7F, 0xF0, 0x00, //  ###########     
	0x18, 0x38, 0x00, //    ##     ###    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x7F, 0xF0, 0x00, //  ###########     
	0x7F, 0xE0, 0x00, //  ##########      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2664 'E' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7F, 0xF8, 0x00, //  ############    
	0x7F, 0xF8, 0x00, //  ############    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x19, 0x98, 0x00, //    ##  ##  ##    
	0x19, 0x80, 0x00, //    ##  ##        
	0x1F, 0x80, 0x00, //    ######        
	0x1F, 0x80, 0x00, //    ######        
	0x19, 0x80, 0x00, //    ##  ##        
	0x19, 0x98, 0x00, //    ##  ##  ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x7F, 0xF8, 0x00, //  ############    
	0x7F, 0xF8, 0x00, //  ############    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2736 'F' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x3F, 0xFC, 0x00, //   ############   
	0x3F, 0xFC, 0x00, //   ############   
	0x0C, 0x0C, 0x00, //     ##      ##   
	0x0C, 0x0C, 0x00, //     ##      ##   
	0x0C, 0xCC, 0x00, //     ##  ##  ##   
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x0F, 0xC0, 0x00, //     ######       
	0x0F, 0xC0, 0x00, //     ######       
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x3F, 0xC0, 0x00, //   ########       
	0x3F, 0xC0, 0x00, //   ########       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2808 'G' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xEC, 0x00, //       ##### ##   
	0x0F, 0xFC, 0x00, //     ##########   
	0x1C, 0x1C, 0x00, //    ###     ###   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x00, 0x00, //   ##             
	0x30, 0x00, 0x00, //   ##             
	0x30, 0xFE, 0x00, //   ##    #######  
	0x30, 0xFE, 0x00, //   ##    #######  
	0x30, 0x0C, 0x00, //   ##        ##   
	0x38, 0x0C, 0x00, //   ###       ##   
	0x1C, 0x1C, 0x00, //    ###     ###   
	0x0F, 0xFC, 0x00, //     ##########   
	0x03, 0xF0, 0x00, //       ######     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2880 'H' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x1F, 0xF8, 0x00, //    ##########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @2952 'I' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0xF8, 0x00, //    ##########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x1F, 0xF8, 0x00, //    ##########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3024 'J' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xFE, 0x00, //      ##########  
	0x07, 0xFE, 0x00, //      ##########  
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x30, 0x30, 0x00, //   ##      ##     
	0x30, 0x30, 0x00, //   ##      ##     
	0x30, 0x30, 0x00, //   ##      ##     
	0x30, 0x30, 0x00, //   ##      ##     
	0x30, 0x60, 0x00, //   ##     ##      
	0x3F, 0xE0, 0x00, //   #########      
	0x0F, 0x80, 0x00, //     #####        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3096 'K' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7F, 0x3E, 0x00, //  #######  #####  
	0x7F, 0x3E, 0x00, //  #######  #####  
	0x18, 0x30, 0x00, //    ##     ##     
	0x18, 0x60, 0x00, //    ##    ##      
	0x18, 0xC0, 0x00, //    ##   ##       
	0x19, 0x80, 0x00, //    ##  ##        
	0x1B, 0x80, 0x00, //    ## ###        
	0x1F, 0xC0, 0x00, //    #######       
	0x1C, 0xE0, 0x00, //    ###  ###      
	0x18, 0x70, 0x00, //    ##    ###     
	0x18, 0x30, 0x00, //    ##     ##     
	0x18, 0x38, 0x00, //    ##     ###    
	0x7F, 0x1F, 0x00, //  #######   ##### 
	0x7F, 0x1F, 0x00, //  #######   ##### 
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3168 'L' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7F, 0x80, 0x00, //  ########        
	0x7F, 0x80, 0x00, //  ########        
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x0C, 0x00, //     ##      ##   
	0x0C, 0x0C, 0x00, //     ##      ##   
	0x0C, 0x0C, 0x00, //     ##      ##   
	0x0C, 0x0C, 0x00, //     ##      ##   
	0x7F, 0xFC, 0x00, //  #############   
	0x7F, 0xFC, 0x00, //  #############   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3240 'M' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0xF0, 0x0F, 0x00, // ####        #### 
	0xF8, 0x1F, 0x00, // #####      ##### 
	0x38, 0x1C, 0x00, //   ###      ###   
	0x3C, 0x3C, 0x00, //   ####    ####   
	0x3C, 0x3C, 0x00, //   ####    ####   
	0x36, 0x6C, 0x00, //   ## ##  ## ##   
	0x36, 0x6C, 0x00, //   ## ##  ## ##   
	0x33, 0xCC, 0x00, //   ##  ####  ##   
	0x33, 0xCC, 0x00, //   ##  ####  ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0xFE, 0x7F, 0x00, // #######  ####### 
	0xFE, 0x7F, 0x00, // #######  ####### 
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3312 'N' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x78, 0xFE, 0x00, //  ####   #######  
	0x78, 0xFE, 0x00, //  ####   #######  
	0x1C, 0x18, 0x00, //    ###     ##    
	0x1E, 0x18, 0x00, //    ####    ##    
	0x1F, 0x18, 0x00, //    #####   ##    
	0x1B, 0x18, 0x00, //    ## ##   ##    
	0x1B, 0x98, 0x00, //    ## ###  ##    
	0x19, 0xD8, 0x00, //    ##  ### ##    
	0x18, 0xD8, 0x00, //    ##   ## ##    
	0x18, 0xF8, 0x00, //    ##   #####    
	0x18, 0x78, 0x00, //    ##    ####    
	0x18, 0x38, 0x00, //    ##     ###    
	0x7F, 0x18, 0x00, //  #######   ##    
	0x7F, 0x18, 0x00, //  #######   ##    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3384 'O' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xC0, 0x00, //       ####       
	0x0F, 0xF0, 0x00, //     ########     
	0x1C, 0x38, 0x00, //    ###    ###    
	0x18, 0x18, 0x00, //    ##      ##    
	0x38, 0x1C, 0x00, //   ###      ###   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x38, 0x1C, 0x00, //   ###      ###   
	0x18, 0x18, 0x00, //    ##      ##    
	0x1C, 0x38, 0x00, //    ###    ###    
	0x0F, 0xF0, 0x00, //     ########     
	0x03, 0xC0, 0x00, //       ####       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3456 'P' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x3F, 0xF0, 0x00, //   ##########     
	0x3F, 0xF8, 0x00, //   ###########    
	0x0C, 0x1C, 0x00, //     ##     ###   
	0x0C, 0x0C, 0x00, //     ##      ##   
	0x0C, 0x0C, 0x00, //     ##      ##   
	0x0C, 0x0C, 0x00, //     ##      ##   
	0x0C, 0x18, 0x00, //     ##     ##    
	0x0F, 0xF8, 0x00, //     #########    
	0x0F, 0xE0, 0x00, //     #######      
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x3F, 0xC0, 0x00, //   ########       
	0x3F, 0xC0, 0x00, //   ########       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3528 'Q' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xC0, 0x00, //       ####       
	0x0F, 0xF0, 0x00, //     ########     
	0x1C, 0x38, 0x00, //    ###    ###    
	0x18, 0x18, 0x00, //    ##      ##    
	0x38, 0x1C, 0x00, //   ###      ###   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x38, 0x1C, 0x00, //   ###      ###   
	0x18, 0x18, 0x00, //    ##      ##    
	0x1C, 0x38, 0x00, //    ###    ###    
	0x0F, 0xF0, 0x00, //     ########     
	0x07, 0xC0, 0x00, //      #####       
	0x07, 0xCC, 0x00, //      #####  ##   
	0x0F, 0xFC, 0x00, //     ##########   
	0x0C, 0x38, 0x00, //     ##    ###    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3600 'R' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7F, 0xE0, 0x00, //  ##########      
	0x7F, 0xF0, 0x00, //  ###########     
	0x18, 0x38, 0x00, //    ##     ###    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x1F, 0xF0, 0x00, //    #########     
	0x1F, 0xC0, 0x00, //    #######       
	0x18, 0xE0, 0x00, //    ##   ###      
	0x18, 0x70, 0x00, //    ##    ###     
	0x18, 0x30, 0x00, //    ##     ##     
	0x18, 0x38, 0x00, //    ##     ###    
	0x7F, 0x1E, 0x00, //  #######   ####  
	0x7F, 0x0E, 0x00, //  #######    ###  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3672 'S' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xD8, 0x00, //      ##### ##    
	0x0F, 0xF8, 0x00, //     #########    
	0x1C, 0x38, 0x00, //    ###    ###    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x1E, 0x00, 0x00, //    ####          
	0x0F, 0xC0, 0x00, //     ######       
	0x03, 0xF0, 0x00, //       ######     
	0x00, 0x78, 0x00, //          ####    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x1C, 0x38, 0x00, //    ###    ###    
	0x1F, 0xF0, 0x00, //    #########     
	0x1B, 0xE0, 0x00, //    ## #####      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3744 'T' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x3F, 0xFC, 0x00, //   ############   
	0x3F, 0xFC, 0x00, //   ############   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x0F, 0xF0, 0x00, //     ########     
	0x0F, 0xF0, 0x00, //     ########     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3816 'U' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x0C, 0x30, 0x00, //     ##    ##     
	0x0F, 0xF0, 0x00, //     ########     
	0x03, 0xC0, 0x00, //       ####       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3888 'V' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7F, 0x7F, 0x00, //  ####### ####### 
	0x7F, 0x7F, 0x00, //  ####### ####### 
	0x18, 0x0C, 0x00, //    ##       ##   
	0x0C, 0x18, 0x00, //     ##     ##    
	0x0C, 0x18, 0x00, //     ##     ##    
	0x0C, 0x18, 0x00, //     ##     ##    
	0x06, 0x30, 0x00, //      ##   ##     
	0x06, 0x30, 0x00, //      ##   ##     
	0x03, 0x60, 0x00, //       ## ##      
	0x03, 0x60, 0x00, //       ## ##      
	0x03, 0x60, 0x00, //       ## ##      
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0xC0, 0x00, //        ###       
	0x00, 0x80, 0x00, //         #        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @3960 'W' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0xFE, 0x3F, 0x80, // #######   #######
	0xFE, 0x3F, 0x80, // #######   #######
	0x30, 0x06, 0x00, //   ##         ##  
	0x30, 0x06, 0x00, //   ##         ##  
	0x30, 0x86, 0x00, //   ##    #    ##  
	0x19, 0xCC, 0x00, //    ##  ###  ##   
	0x19, 0xCC, 0x00, //    ##  ###  ##   
	0x1B, 0x6C, 0x00, //    ## ## ## ##   
	0x1B, 0x6C, 0x00, //    ## ## ## ##   
	0x1E, 0x7C, 0x00, //    ####  #####   
	0x0E, 0x38, 0x00, //     ###   ###    
	0x0E, 0x38, 0x00, //     ###   ###    
	0x0C, 0x18, 0x00, //     ##     ##    
	0x0C, 0x18, 0x00, //     ##     ##    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4032 'X' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x18, 0x18, 0x00, //    ##      ##    
	0x0C, 0x30, 0x00, //     ##    ##     
	0x06, 0x60, 0x00, //      ##  ##      
	0x03, 0xC0, 0x00, //       ####       
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x03, 0xC0, 0x00, //       ####       
	0x06, 0x60, 0x00, //      ##  ##      
	0x0C, 0x30, 0x00, //     ##    ##     
	0x18, 0x18, 0x00, //    ##      ##    
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4104 'Y' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7C, 0x7E, 0x00, //  #####   ######  
	0x7C, 0x7E, 0x00, //  #####   ######  
	0x18, 0x18, 0x00, //    ##      ##    
	0x0C, 0x30, 0x00, //     ##    ##     
	0x06, 0x60, 0x00, //      ##  ##      
	0x06, 0x60, 0x00, //      ##  ##      
	0x03, 0xC0, 0x00, //       ####       
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x0F, 0xF0, 0x00, //     ########     
	0x0F, 0xF0, 0x00, //     ########     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4176 'Z' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0xF8, 0x00, //    ##########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x30, 0x00, //    ##     ##     
	0x18, 0x60, 0x00, //    ##    ##      
	0x18, 0xC0, 0x00, //    ##   ##       
	0x01, 0x80, 0x00, //        ##        
	0x03, 0x00, 0x00, //       ##         
	0x06, 0x18, 0x00, //      ##    ##    
	0x0C, 0x18, 0x00, //     ##     ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x3F, 0xF8, 0x00, //   ###########    
	0x3F, 0xF8, 0x00, //   ###########    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4248 '[' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x01, 0xF0, 0x00, //        #####     
	0x01, 0xF0, 0x00, //        #####     
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0xF0, 0x00, //        #####     
	0x01, 0xF0, 0x00, //        #####     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4320 '\' (17 pixels wide)
	0x18, 0x00, 0x00, //    ##            
	0x18, 0x00, 0x00, //    ##            
	0x1C, 0x00, 0x00, //    ###           
	0x0C, 0x00, 0x00, //     ##           
	0x0E, 0x00, 0x00, //     ###          
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x03, 0x00, 0x00, //       ##         
	0x03, 0x00, 0x00, //       ##         
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x00, 0xC0, 0x00, //         ##       
	0x00, 0xC0, 0x00, //         ##       
	0x00, 0x60, 0x00, //          ##      
	0x00, 0x60, 0x00, //          ##      
	0x00, 0x70, 0x00, //          ###     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x38, 0x00, //           ###    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4392 ']' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x0F, 0x80, 0x00, //     #####        
	0x0F, 0x80, 0x00, //     #####        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x0F, 0x80, 0x00, //     #####        
	0x0F, 0x80, 0x00, //     #####        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4464 '^' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x80, 0x00, //         #        
	0x01, 0xC0, 0x00, //        ###       
	0x03, 0xE0, 0x00, //       #####      
	0x07, 0x70, 0x00, //      ### ###     
	0x06, 0x30, 0x00, //      ##   ##     
	0x0C, 0x18, 0x00, //     ##     ##    
	0x18, 0x0C, 0x00, //    ##       ##   
	0x10, 0x04, 0x00, //    #         #   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4536 '_' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0xFF, 0xFF, 0x00, // ################ 
	0xFF, 0xFF, 0x00, // ################ 

	// @4608 '`' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x03, 0x00, 0x00, //       ##         
	0x03, 0x80, 0x00, //       ###        
	0x00, 0xE0, 0x00, //         ###      
	0x00, 0x60, 0x00, //          ##      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4680 'a' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x0F, 0xC0, 0x00, //     ######       
	0x1F, 0xE0, 0x00, //    ########      
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x07, 0xF0, 0x00, //      #######     
	0x1F, 0xF0, 0x00, //    #########     
	0x38, 0x30, 0x00, //   ###     ##     
	0x30, 0x30, 0x00, //   ##      ##     
	0x30, 0x70, 0x00, //   ##     ###     
	0x1F, 0xFC, 0x00, //    ###########   
	0x0F, 0xBC, 0x00, //     ##### ####   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4752 'b' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x78, 0x00, 0x00, //  ####            
	0x78, 0x00, 0x00, //  ####            
	0x18, 0x00, 0x00, //    ##            
	0x18, 0x00, 0x00, //    ##            
	0x1B, 0xE0, 0x00, //    ## #####      
	0x1F, 0xF8, 0x00, //    ##########    
	0x1C, 0x18, 0x00, //    ###     ##    
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x1C, 0x18, 0x00, //    ###     ##    
	0x7F, 0xF8, 0x00, //  ############    
	0x7B, 0xE0, 0x00, //  #### #####      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4824 'c' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xEC, 0x00, //       ##### ##   
	0x0F, 0xFC, 0x00, //     ##########   
	0x1C, 0x1C, 0x00, //    ###     ###   
	0x38, 0x0C, 0x00, //   ###       ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x00, 0x00, //   ##             
	0x30, 0x00, 0x00, //   ##             
	0x38, 0x0C, 0x00, //   ###       ##   
	0x1C, 0x1C, 0x00, //    ###     ###   
	0x0F, 0xF8, 0x00, //     #########    
	0x03, 0xF0, 0x00, //       ######     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4896 'd' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x78, 0x00, //          ####    
	0x00, 0x78, 0x00, //          ####    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x07, 0xD8, 0x00, //      ##### ##    
	0x1F, 0xF8, 0x00, //    ##########    
	0x18, 0x38, 0x00, //    ##     ###    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x1F, 0xFE, 0x00, //    ############  
	0x07, 0xDE, 0x00, //      ##### ####  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @4968 'e' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xE0, 0x00, //      ######      
	0x1F, 0xF8, 0x00, //    ##########    
	0x18, 0x18, 0x00, //    ##      ##    
	0x30, 0x0C, 0x00, //   ##        ##   
	0x3F, 0xFC, 0x00, //   ############   
	0x3F, 0xFC, 0x00, //   ############   
	0x30, 0x00, 0x00, //   ##             
	0x30, 0x00, 0x00, //   ##             
	0x18, 0x0C, 0x00, //    ##       ##   
	0x1F, 0xFC, 0x00, //    ###########   
	0x07, 0xF0, 0x00, //      #######     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5040 'f' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x01, 0xFC, 0x00, //        #######   
	0x03, 0xFC, 0x00, //       ########   
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x3F, 0xF8, 0x00, //   ###########    
	0x3F, 0xF8, 0x00, //   ###########    
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x3F, 0xF0, 0x00, //   ##########     
	0x3F, 0xF0, 0x00, //   ##########     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5112 'g' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xDE, 0x00, //      ##### ####  
	0x1F, 0xFE, 0x00, //    ############  
	0x18, 0x38, 0x00, //    ##     ###    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x1F, 0xF8, 0x00, //    ##########    
	0x07, 0xD8, 0x00, //      ##### ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x38, 0x00, //           ###    
	0x0F, 0xF0, 0x00, //     ########     
	0x0F, 0xC0, 0x00, //     ######       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5184 'h' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x78, 0x00, 0x00, //  ####            
	0x78, 0x00, 0x00, //  ####            
	0x18, 0x00, 0x00, //    ##            
	0x18, 0x00, 0x00, //    ##            
	0x1B, 0xE0, 0x00, //    ## #####      
	0x1F, 0xF0, 0x00, //    #########     
	0x1C, 0x38, 0x00, //    ###    ###    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5256 'i' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0x80, 0x00, //    ######        
	0x1F, 0x80, 0x00, //    ######        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x3F, 0xFC, 0x00, //   ############   
	0x3F, 0xFC, 0x00, //   ############   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5328 'j' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0xC0, 0x00, //         ##       
	0x00, 0xC0, 0x00, //         ##       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0xF0, 0x00, //    #########     
	0x1F, 0xF0, 0x00, //    #########     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x30, 0x00, //           ##     
	0x00, 0x70, 0x00, //          ###     
	0x1F, 0xE0, 0x00, //    ########      
	0x1F, 0x80, 0x00, //    ######        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5400 'k' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x3C, 0x00, 0x00, //   ####           
	0x3C, 0x00, 0x00, //   ####           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0xF8, 0x00, //     ##  #####    
	0x0C, 0xF8, 0x00, //     ##  #####    
	0x0C, 0xC0, 0x00, //     ##  ##       
	0x0D, 0x80, 0x00, //     ## ##        
	0x0F, 0x80, 0x00, //     #####        
	0x0F, 0x00, 0x00, //     ####         
	0x0F, 0x80, 0x00, //     #####        
	0x0D, 0xC0, 0x00, //     ## ###       
	0x0C, 0xE0, 0x00, //     ##  ###      
	0x3C, 0x7C, 0x00, //   ####   #####   
	0x3C, 0x7C, 0x00, //   ####   #####   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5472 'l' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0x80, 0x00, //    ######        
	0x1F, 0x80, 0x00, //    ######        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x3F, 0xFC, 0x00, //   ############   
	0x3F, 0xFC, 0x00, //   ############   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5544 'm' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0xF7, 0x78, 0x00, // #### ### ####    
	0xFF, 0xFC, 0x00, // ##############   
	0x39, 0xCC, 0x00, //   ###  ###  ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0x31, 0x8C, 0x00, //   ##   ##   ##   
	0xFD, 0xEF, 0x00, // ###### #### #### 
	0xFD, 0xEF, 0x00, // ###### #### #### 
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5616 'n' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7B, 0xE0, 0x00, //  #### #####      
	0x7F, 0xF0, 0x00, //  ###########     
	0x1C, 0x38, 0x00, //    ###    ###    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x7E, 0x7E, 0x00, //  ######  ######  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5688 'o' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x03, 0xC0, 0x00, //       ####       
	0x0F, 0xF0, 0x00, //     ########     
	0x1C, 0x38, 0x00, //    ###    ###    
	0x38, 0x1C, 0x00, //   ###      ###   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x30, 0x0C, 0x00, //   ##        ##   
	0x38, 0x1C, 0x00, //   ###      ###   
	0x1C, 0x38, 0x00, //    ###    ###    
	0x0F, 0xF0, 0x00, //     ########     
	0x03, 0xC0, 0x00, //       ####       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5760 'p' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7B, 0xE0, 0x00, //  #### #####      
	0x7F, 0xF8, 0x00, //  ############    
	0x1C, 0x18, 0x00, //    ###     ##    
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x18, 0x0C, 0x00, //    ##       ##   
	0x1C, 0x18, 0x00, //    ###     ##    
	0x1F, 0xF8, 0x00, //    ##########    
	0x1B, 0xE0, 0x00, //    ## #####      
	0x18, 0x00, 0x00, //    ##            
	0x18, 0x00, 0x00, //    ##            
	0x18, 0x00, 0x00, //    ##            
	0x7F, 0x00, 0x00, //  #######         
	0x7F, 0x00, 0x00, //  #######         
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5832 'q' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xDE, 0x00, //      ##### ####  
	0x1F, 0xFE, 0x00, //    ############  
	0x18, 0x38, 0x00, //    ##     ###    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x30, 0x18, 0x00, //   ##       ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x1F, 0xF8, 0x00, //    ##########    
	0x07, 0xD8, 0x00, //      ##### ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0x18, 0x00, //            ##    
	0x00, 0xFE, 0x00, //         #######  
	0x00, 0xFE, 0x00, //         #######  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5904 'r' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x3E, 0x78, 0x00, //   #####  ####    
	0x3E, 0xFC, 0x00, //   ##### ######   
	0x07, 0xCC, 0x00, //      #####  ##   
	0x07, 0x00, 0x00, //      ###         
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x06, 0x00, 0x00, //      ##          
	0x3F, 0xF0, 0x00, //   ##########     
	0x3F, 0xF0, 0x00, //   ##########     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @5976 's' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0xF8, 0x00, //      ########    
	0x0F, 0xF8, 0x00, //     #########    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x1F, 0x80, 0x00, //    ######        
	0x0F, 0xF0, 0x00, //     ########     
	0x00, 0xF8, 0x00, //         #####    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x1F, 0xF0, 0x00, //    #########     
	0x1F, 0xE0, 0x00, //    ########      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6048 't' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x3F, 0xF0, 0x00, //   ##########     
	0x3F, 0xF0, 0x00, //   ##########     
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x00, 0x00, //     ##           
	0x0C, 0x1C, 0x00, //     ##     ###   
	0x07, 0xFC, 0x00, //      #########   
	0x03, 0xF0, 0x00, //       ######     
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6120 'u' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x78, 0x78, 0x00, //  ####    ####    
	0x78, 0x78, 0x00, //  ####    ####    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x38, 0x00, //    ##     ###    
	0x0F, 0xFE, 0x00, //     ###########  
	0x07, 0xDE, 0x00, //      ##### ####  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6192 'v' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7C, 0x3E, 0x00, //  #####    #####  
	0x7C, 0x3E, 0x00, //  #####    #####  
	0x18, 0x18, 0x00, //    ##      ##    
	0x18, 0x18, 0x00, //    ##      ##    
	0x0C, 0x30, 0x00, //     ##    ##     
	0x0C, 0x30, 0x00, //     ##    ##     
	0x06, 0x60, 0x00, //      ##  ##      
	0x06, 0x60, 0x00, //      ##  ##      
	0x07, 0xE0, 0x00, //      ######      
	0x03, 0xC0, 0x00, //       ####       
	0x03, 0xC0, 0x00, //       ####       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6264 'w' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x78, 0x3C, 0x00, //  ####     ####   
	0x78, 0x3C, 0x00, //  ####     ####   
	0x31, 0x18, 0x00, //   ##   #   ##    
	0x33, 0x98, 0x00, //   ##  ###  ##    
	0x33, 0x98, 0x00, //   ##  ###  ##    
	0x1A, 0xB0, 0x00, //    ## # # ##     
	0x1E, 0xF0, 0x00, //    #### ####     
	0x1E, 0xF0, 0x00, //    #### ####     
	0x1C, 0x60, 0x00, //    ###   ##      
	0x0C, 0x60, 0x00, //     ##   ##      
	0x0C, 0x60, 0x00, //     ##   ##      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6336 'x' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x3E, 0x7C, 0x00, //   #####  #####   
	0x3E, 0x7C, 0x00, //   #####  #####   
	0x0C, 0x30, 0x00, //     ##    ##     
	0x06, 0x60, 0x00, //      ##  ##      
	0x03, 0xC0, 0x00, //       ####       
	0x01, 0x80, 0x00, //        ##        
	0x03, 0xC0, 0x00, //       ####       
	0x06, 0x60, 0x00, //      ##  ##      
	0x0C, 0x30, 0x00, //     ##    ##     
	0x3E, 0x7C, 0x00, //   #####  #####   
	0x3E, 0x7C, 0x00, //   #####  #####   
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6408 'y' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x7E, 0x1F, 0x00, //  ######    ##### 
	0x7E, 0x1F, 0x00, //  ######    ##### 
	0x18, 0x0C, 0x00, //    ##       ##   
	0x0C, 0x18, 0x00, //     ##     ##    
	0x0C, 0x18, 0x00, //     ##     ##    
	0x06, 0x30, 0x00, //      ##   ##     
	0x06, 0x30, 0x00, //      ##   ##     
	0x03, 0x60, 0x00, //       ## ##      
	0x03, 0xE0, 0x00, //       #####      
	0x01, 0xC0, 0x00, //        ###       
	0x00, 0xC0, 0x00, //         ##       
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x03, 0x00, 0x00, //       ##         
	0x3F, 0xC0, 0x00, //   ########       
	0x3F, 0xC0, 0x00, //   ########       
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6480 'z' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x1F, 0xF8, 0x00, //    ##########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x18, 0x30, 0x00, //    ##     ##     
	0x18, 0x60, 0x00, //    ##    ##      
	0x00, 0xC0, 0x00, //         ##       
	0x01, 0x80, 0x00, //        ##        
	0x03, 0x00, 0x00, //       ##         
	0x06, 0x18, 0x00, //      ##    ##    
	0x0C, 0x18, 0x00, //     ##     ##    
	0x1F, 0xF8, 0x00, //    ##########    
	0x1F, 0xF8, 0x00, //    ##########    
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6552 '{' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0xE0, 0x00, //         ###      
	0x01, 0xE0, 0x00, //        ####      
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x03, 0x80, 0x00, //       ###        
	0x07, 0x00, 0x00, //      ###         
	0x03, 0x80, 0x00, //       ###        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0xE0, 0x00, //        ####      
	0x00, 0xE0, 0x00, //         ###      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6624 '|' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6696 '}' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x07, 0x00, 0x00, //      ###         
	0x07, 0x80, 0x00, //      ####        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0xC0, 0x00, //        ###       
	0x00, 0xE0, 0x00, //         ###      
	0x01, 0xC0, 0x00, //        ###       
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x01, 0x80, 0x00, //        ##        
	0x07, 0x80, 0x00, //      ####        
	0x07, 0x00, 0x00, //      ###         
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  

	// @6768 '~' (17 pixels wide)
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x0E, 0x00, 0x00, //     ###          
	0x1F, 0x18, 0x00, //    #####   ##    
	0x3B, 0xB8, 0x00, //   ### ### ###    
	0x31, 0xF0, 0x00, //   ##   #####     
	0x00, 0xE0, 0x00, //         ###      
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
	0x00, 0x00, 0x00, //                  
};

sFONT Font24 = {
  Font24_Table,
  17, /* Width */
  24, /* Height */
};

/**
  * @}
  */ 
  
/**
  * @}
  */ 

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */  
/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
