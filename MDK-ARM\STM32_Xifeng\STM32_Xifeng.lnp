--cpu=Cortex-M4.fp.sp
"stm32_xifeng\startup_stm32g431xx.o"
"stm32_xifeng\main.o"
"stm32_xifeng\gpio.o"
"stm32_xifeng\adc.o"
"stm32_xifeng\dma.o"
"stm32_xifeng\rtc.o"
"stm32_xifeng\tim.o"
"stm32_xifeng\usart.o"
"stm32_xifeng\stm32g4xx_it.o"
"stm32_xifeng\stm32g4xx_hal_msp.o"
"stm32_xifeng\stm32g4xx_hal_adc.o"
"stm32_xifeng\stm32g4xx_hal_adc_ex.o"
"stm32_xifeng\stm32g4xx_ll_adc.o"
"stm32_xifeng\stm32g4xx_hal.o"
"stm32_xifeng\stm32g4xx_hal_rcc.o"
"stm32_xifeng\stm32g4xx_hal_rcc_ex.o"
"stm32_xifeng\stm32g4xx_hal_flash.o"
"stm32_xifeng\stm32g4xx_hal_flash_ex.o"
"stm32_xifeng\stm32g4xx_hal_flash_ramfunc.o"
"stm32_xifeng\stm32g4xx_hal_gpio.o"
"stm32_xifeng\stm32g4xx_hal_exti.o"
"stm32_xifeng\stm32g4xx_hal_dma.o"
"stm32_xifeng\stm32g4xx_hal_dma_ex.o"
"stm32_xifeng\stm32g4xx_hal_pwr.o"
"stm32_xifeng\stm32g4xx_hal_pwr_ex.o"
"stm32_xifeng\stm32g4xx_hal_cortex.o"
"stm32_xifeng\stm32g4xx_hal_rtc.o"
"stm32_xifeng\stm32g4xx_hal_rtc_ex.o"
"stm32_xifeng\stm32g4xx_hal_tim.o"
"stm32_xifeng\stm32g4xx_hal_tim_ex.o"
"stm32_xifeng\stm32g4xx_hal_uart.o"
"stm32_xifeng\stm32g4xx_hal_uart_ex.o"
"stm32_xifeng\system_stm32g4xx.o"
"stm32_xifeng\scheduler.o"
"stm32_xifeng\key_app.o"
"stm32_xifeng\lcd.o"
"stm32_xifeng\lcd_app.o"
"stm32_xifeng\led_app.o"
"stm32_xifeng\system.o"
"stm32_xifeng\uart_app.o"
"stm32_xifeng\ringbuffer.o"
"stm32_xifeng\adc_app.o"
"stm32_xifeng\filter.o"
"stm32_xifeng\i2c_hal.o"
"stm32_xifeng\rtc_app.o"
"stm32_xifeng\tim_app.o"
"stm32_xifeng\fft_app.o"
"stm32_xifeng\calibration.o"
"..\Middlewares\ST\ARM\DSP\Lib\arm_cortexM4lf_math.lib"
--library_type=microlib --strict --scatter "STM32_Xifeng\STM32_Xifeng.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "STM32_Xifeng.map" -o STM32_Xifeng\STM32_Xifeng.axf