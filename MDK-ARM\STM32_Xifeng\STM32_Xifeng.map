Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(.text) for Reset_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DMA1_Channel3_IRQHandler) for DMA1_Channel3_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DMA1_Channel4_IRQHandler) for DMA1_Channel4_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.ADC1_2_IRQHandler) for ADC1_2_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32g431xx.o(.text) refers to system_stm32g4xx.o(i.SystemInit) for SystemInit
    startup_stm32g431xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32g4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to adc.o(i.MX_ADC2_Init) for MX_ADC2_Init
    main.o(i.main) refers to rtc.o(i.MX_RTC_Init) for MX_RTC_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to system.o(i.system_init) for system_init
    main.o(i.main) refers to key_app.o(i.key_init) for key_init
    main.o(i.main) refers to ringbuffer.o(i.ringbuffer_init) for ringbuffer_init
    main.o(i.main) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) for HAL_TIM_IC_Start_DMA
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(i.main) refers to lcd.o(i.LCD_Init) for LCD_Init
    main.o(i.main) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    main.o(i.main) refers to lcd.o(i.LCD_SetTextColor) for LCD_SetTextColor
    main.o(i.main) refers to lcd.o(i.LCD_SetBackColor) for LCD_SetBackColor
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to uart_app.o(.bss) for usart_rb
    main.o(i.main) refers to adc_app.o(.bss) for dma_buff
    main.o(i.main) refers to adc.o(.bss) for hadc1
    main.o(i.main) refers to tim_app.o(.bss) for tim_ic_buffer
    main.o(i.main) refers to tim.o(.bss) for htim3
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to adc.o(.data) for HAL_RCC_ADC12_CLK_ENABLED
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.data) for HAL_RCC_ADC12_CLK_ENABLED
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for hdma_adc1
    adc.o(i.MX_ADC1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.MX_ADC1_Init) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) for HAL_ADCEx_MultiModeConfigChannel
    adc.o(i.MX_ADC1_Init) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for hadc1
    adc.o(i.MX_ADC2_Init) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.MX_ADC2_Init) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC2_Init) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC2_Init) refers to adc.o(.bss) for hadc2
    dma.o(i.MX_DMA_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    rtc.o(i.HAL_RTC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    rtc.o(i.HAL_RTC_MspInit) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    rtc.o(i.HAL_RTC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    rtc.o(i.MX_RTC_Init) refers to memseta.o(.text) for __aeabi_memclr4
    rtc.o(i.MX_RTC_Init) refers to stm32g4xx_hal_rtc.o(i.HAL_RTC_Init) for HAL_RTC_Init
    rtc.o(i.MX_RTC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    rtc.o(i.MX_RTC_Init) refers to stm32g4xx_hal_rtc.o(i.HAL_RTC_SetTime) for HAL_RTC_SetTime
    rtc.o(i.MX_RTC_Init) refers to stm32g4xx_hal_rtc.o(i.HAL_RTC_SetDate) for HAL_RTC_SetDate
    rtc.o(i.MX_RTC_Init) refers to rtc.o(.bss) for hrtc
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    tim.o(i.HAL_TIM_Base_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    tim.o(i.HAL_TIM_Base_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.HAL_TIM_Base_MspInit) refers to tim.o(.bss) for hdma_tim3_ch1
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for htim1
    tim.o(i.MX_TIM2_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM2_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for htim2
    tim.o(i.MX_TIM3_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) for HAL_TIM_SlaveConfigSynchro
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for htim3
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for hdma_usart1_rx
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.fputc) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for huart1
    stm32g4xx_it.o(i.ADC1_2_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32g4xx_it.o(i.ADC1_2_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32g4xx_it.o(i.DMA1_Channel2_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(i.DMA1_Channel2_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32g4xx_it.o(i.DMA1_Channel3_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(i.DMA1_Channel3_IRQHandler) refers to adc.o(.bss) for hdma_adc2
    stm32g4xx_it.o(i.DMA1_Channel4_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(i.DMA1_Channel4_IRQHandler) refers to tim.o(.bss) for hdma_tim3_ch1
    stm32g4xx_it.o(i.SysTick_Handler) refers to stm32g4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32g4xx_it.o(i.USART1_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32g4xx_it.o(i.USART1_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32g4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32g4xx_hal_msp.o(i.HAL_MspInit) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery) for HAL_PWREx_DisableUCPDDeadBattery
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing) for LL_ADC_IsDisableOngoing
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(i.ADC_DMAError) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32g4xx_hal_adc.o(i.ADC_Disable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing) for LL_ADC_IsDisableOngoing
    stm32g4xx_hal_adc.o(i.ADC_Disable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.ADC_Disable) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_Enable) for LL_ADC_Enable
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels) for LL_ADC_SetAnalogWDMonitChannels
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonConfig) for LL_ADC_SetSamplingTimeCommonConfig
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh) for LL_ADC_SetCommonPathInternalCh
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled) for LL_ADC_IsInternalRegulatorEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetSamplingTimeCommonConfig) for LL_ADC_SetSamplingTimeCommonConfig
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetCommonPathInternalCh) for LL_ADC_SetCommonPathInternalCh
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_GetTrigAuto) for LL_ADC_INJ_GetTrigAuto
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_StartConversion) for LL_ADC_INJ_StartConversion
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_GetTrigAuto) for LL_ADC_INJ_GetTrigAuto
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_StartConversion) for LL_ADC_INJ_StartConversion
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal.o(i.HAL_DeInit) refers to stm32g4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal.o(i.HAL_GetTick) refers to stm32g4xx_hal.o(.data) for uwTick
    stm32g4xx_hal.o(i.HAL_GetTickFreq) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal.o(i.HAL_GetTickPrio) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal.o(i.HAL_IncTick) refers to stm32g4xx_hal.o(.data) for uwTick
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal.o(i.HAL_InitTick) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal_rcc.o(i.RCC_GetSysClockFreqFromPLLSource) for RCC_GetSysClockFreqFromPLLSource
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.constdata) for AHBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_BootLockConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_SecMemConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP) for FLASH_OB_GetPCROP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetBootLock) for FLASH_OB_GetBootLock
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetSecMem) for FLASH_OB_GetSecMem
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig) for FLASH_OB_WRPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) for FLASH_OB_RDPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) for FLASH_OB_PCROPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_SecMemConfig) for FLASH_OB_SecMemConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_BootLockConfig) for FLASH_OB_BootLockConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback) for HAL_PWREx_PVM1Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback) for HAL_PWREx_PVM2Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32g4xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32g4xx_hal_rtc.o(i.HAL_RTC_AlarmAEventCallback) for HAL_RTC_AlarmAEventCallback
    stm32g4xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_AlarmBEventCallback) for HAL_RTCEx_AlarmBEventCallback
    stm32g4xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32g4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32g4xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32g4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32g4xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to rtc.o(i.HAL_RTC_MspDeInit) for HAL_RTC_MspDeInit
    stm32g4xx_hal_rtc.o(i.HAL_RTC_DeactivateAlarm) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(i.HAL_RTC_GetAlarm) refers to stm32g4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32g4xx_hal_rtc.o(i.HAL_RTC_GetDate) refers to stm32g4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32g4xx_hal_rtc.o(i.HAL_RTC_GetTime) refers to stm32g4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32g4xx_hal_rtc.o(i.HAL_RTC_Init) refers to rtc.o(i.HAL_RTC_MspInit) for HAL_RTC_MspInit
    stm32g4xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32g4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32g4xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32g4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc.o(i.HAL_RTC_PollForAlarmAEvent) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32g4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to stm32g4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32g4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32g4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32g4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32g4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32g4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32g4xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32g4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(i.RTC_EnterInitMode) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(i.RTC_ExitInitMode) refers to stm32g4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock) refers to stm32g4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock) refers to stm32g4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateWakeUpTimer) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_GetTimeStamp) refers to stm32g4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForAlarmBEvent) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForInternalTamperEvent) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamperEvent) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTimeStampEvent) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForWakeUpTimerEvent) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock) refers to stm32g4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock) refers to stm32g4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSmoothCalib) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32g4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper1EventCallback) for HAL_RTCEx_Tamper1EventCallback
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper2EventCallback) for HAL_RTCEx_Tamper2EventCallback
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper3EventCallback) for HAL_RTCEx_Tamper3EventCallback
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_InternalTamper3EventCallback) for HAL_RTCEx_InternalTamper3EventCallback
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_InternalTamper4EventCallback) for HAL_RTCEx_InternalTamper4EventCallback
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_InternalTamper5EventCallback) for HAL_RTCEx_InternalTamper5EventCallback
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_InternalTamper6EventCallback) for HAL_RTCEx_InternalTamper6EventCallback
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampEventCallback) for HAL_RTCEx_TimeStampEventCallback
    stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback) for HAL_RTCEx_WakeUpTimerEventCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback) for HAL_TIMEx_EncoderIndexCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback) for HAL_TIMEx_DirectionChangeCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback) for HAL_TIMEx_IndexErrorCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback) for HAL_TIMEx_TransitionErrorCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAError) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigEncoderIndex) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_uart.o(.constdata) for UARTPrescTable
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig) for UARTEx_Wakeup_AddressConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) refers to stm32g4xx_hal_uart_ex.o(.constdata) for numerator
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.constdata) for AHBPrescTable
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for task_num
    scheduler.o(i.scheduler_run) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for scheduler_task
    scheduler.o(.data) refers to led_app.o(i.led_proc) for led_proc
    scheduler.o(.data) refers to key_app.o(i.key_proc) for key_proc
    scheduler.o(.data) refers to lcd_app.o(i.lcd_proc) for lcd_proc
    scheduler.o(.data) refers to uart_app.o(i.uart_proc) for uart_proc
    scheduler.o(.data) refers to adc_app.o(i.adc_proc) for adc_proc
    scheduler.o(.data) refers to rtc_app.o(i.rtc_proc) for rtc_proc
    scheduler.o(.data) refers to tim_app.o(i.ic_proc) for ic_proc
    key_app.o(i.key_init) refers to key_app.o(.bss) for btns
    key_app.o(i.key_proc) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_proc) refers to key_app.o(.data) for key_val
    key_app.o(i.key_proc) refers to lcd_app.o(.data) for lcd_mode
    key_app.o(i.key_proc) refers to adc_app.o(.data) for input_change_flag
    key_app.o(i.key_proc) refers to led_app.o(.data) for ucLed
    key_app.o(i.key_read) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.key_state) refers to key_app.o(i.key_task) for key_task
    key_app.o(i.key_state) refers to key_app.o(.bss) for btns
    key_app.o(i.key_task) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.key_task) refers to led_app.o(.data) for ucLed
    lcd.o(i.LCD_BusIn) refers to memseta.o(.text) for __aeabi_memclr4
    lcd.o(i.LCD_BusIn) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_BusOut) refers to memseta.o(.text) for __aeabi_memclr4
    lcd.o(i.LCD_BusOut) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_ClearLine) refers to lcd.o(i.LCD_DisplayStringLine) for LCD_DisplayStringLine
    lcd.o(i.LCD_ClearRegion) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_ClearRegion) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_CtrlLinesConfig) refers to memseta.o(.text) for __aeabi_memclr4
    lcd.o(i.LCD_CtrlLinesConfig) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_CtrlLinesConfig) refers to lcd.o(i.LCD_BusOut) for LCD_BusOut
    lcd.o(i.LCD_DisplayChar) refers to lcd.o(i.LCD_DrawChar) for LCD_DrawChar
    lcd.o(i.LCD_DisplayChar) refers to lcd.o(.constdata) for ASCII_Table
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayString) refers to lcd.o(i.LCD_DisplayChar) for LCD_DisplayChar
    lcd.o(i.LCD_DisplayStringLine) refers to lcd.o(i.LCD_DisplayChar) for LCD_DisplayChar
    lcd.o(i.LCD_DrawChar) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawChar) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawChar) refers to lcd.o(.data) for BackColor
    lcd.o(i.LCD_DrawCircle) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawCircle) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawCircle) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawCircle) refers to lcd.o(.data) for TextColor
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawLine) refers to lcd.o(.data) for TextColor
    lcd.o(i.LCD_DrawLineAny) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawLineAny) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawLineAny) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawLineAny) refers to lcd.o(.data) for TextColor
    lcd.o(i.LCD_DrawMonoPict) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawMonoPict) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawMonoPict) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawMonoPict) refers to lcd.o(.data) for BackColor
    lcd.o(i.LCD_DrawPicture) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawPicture) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawPicture) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawRect) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_CtrlLinesConfig) for LCD_CtrlLinesConfig
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_ReadReg) for LCD_ReadReg
    lcd.o(i.LCD_Init) refers to lcd.o(i.REG_8230_Init) for REG_8230_Init
    lcd.o(i.LCD_Init) refers to lcd.o(i.REG_932X_Init) for REG_932X_Init
    lcd.o(i.LCD_Init) refers to lcd.o(.data) for dummy
    lcd.o(i.LCD_PowerOn) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_PowerOn) refers to lcd.o(i.Delay_LCD) for Delay_LCD
    lcd.o(i.LCD_ReadRAM) refers to lcd.o(i.LCD_BusIn) for LCD_BusIn
    lcd.o(i.LCD_ReadRAM) refers to lcd.o(i.LCD_BusOut) for LCD_BusOut
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_BusIn) for LCD_BusIn
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_BusOut) for LCD_BusOut
    lcd.o(i.LCD_SetBackColor) refers to lcd.o(.data) for BackColor
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_SetDisplayWindow) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_SetDisplayWindow) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_SetTextColor) refers to lcd.o(.data) for TextColor
    lcd.o(i.LCD_WindowModeDisable) refers to lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lcd.o(i.LCD_WindowModeDisable) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_WriteBMP) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_WriteBMP) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_WriteBMP) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_WriteRAM_Prepare) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_WriteReg) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.REG_8230_Init) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.REG_8230_Init) refers to lcd.o(i.Delay_LCD) for Delay_LCD
    lcd.o(i.REG_932X_Init) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.REG_932X_Init) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.my_LCD_DrawLine) refers to lcd.o(i.LCD_DrawLineAny) for LCD_DrawLineAny
    lcd_app.o(i.DisplaySimpleAutoAdjustedWaveform) refers to lcd.o(i.my_LCD_DrawLine) for my_LCD_DrawLine
    lcd_app.o(i.DisplaySpectrum) refers to lcd_app.o(i.map) for map
    lcd_app.o(i.DisplaySpectrum) refers to lcd.o(i.my_LCD_DrawLine) for my_LCD_DrawLine
    lcd_app.o(i.DisplayWaveform) refers to lcd_app.o(i.map) for map
    lcd_app.o(i.DisplayWaveform) refers to lcd.o(i.my_LCD_DrawLine) for my_LCD_DrawLine
    lcd_app.o(i.LcdSprintf) refers to printfa.o(i.__0vsprintf) for vsprintf
    lcd_app.o(i.LcdSprintf) refers to lcd.o(i.LCD_DisplayStringLine) for LCD_DisplayStringLine
    lcd_app.o(i.cusum_detect) refers to lcd_app.o(.data) for cumulative_sum
    lcd_app.o(i.cusum_detect_duty) refers to lcd_app.o(.data) for cumulative_sum
    lcd_app.o(i.lcd_proc) refers to fft_app.o(i.find_peak_frequency) for find_peak_frequency
    lcd_app.o(i.lcd_proc) refers to lcd_app.o(i.cusum_detect) for cusum_detect
    lcd_app.o(i.lcd_proc) refers to lcd_app.o(i.calculate_duty_cycle) for calculate_duty_cycle
    lcd_app.o(i.lcd_proc) refers to lcd_app.o(i.cusum_detect_duty) for cusum_detect_duty
    lcd_app.o(i.lcd_proc) refers to f2d.o(.text) for __aeabi_f2d
    lcd_app.o(i.lcd_proc) refers to lcd_app.o(i.LcdSprintf) for LcdSprintf
    lcd_app.o(i.lcd_proc) refers to lcd.o(i.LCD_ClearRegion) for LCD_ClearRegion
    lcd_app.o(i.lcd_proc) refers to lcd_app.o(i.DisplayWaveform) for DisplayWaveform
    lcd_app.o(i.lcd_proc) refers to lcd_app.o(i.DisplaySimpleAutoAdjustedWaveform) for DisplaySimpleAutoAdjustedWaveform
    lcd_app.o(i.lcd_proc) refers to lcd_app.o(i.DisplaySpectrum) for DisplaySpectrum
    lcd_app.o(i.lcd_proc) refers to lcd_app.o(.data) for lcd_frequence
    lcd_app.o(i.lcd_proc) refers to adc_app.o(.data) for input_change_flag
    lcd_app.o(i.lcd_proc) refers to adc_app.o(.bss) for dma_buff
    lcd_app.o(i.lcd_proc) refers to lcd_app.o(.bss) for lcd_show_buff
    lcd_app.o(i.lcd_proc) refers to fft_app.o(.bss) for fft_output
    led_app.o(i.led_disp) refers to led_app.o(.data) for temp_old
    led_app.o(i.led_proc) refers to led_app.o(i.led_disp) for led_disp
    led_app.o(i.led_proc) refers to led_app.o(.data) for ucLed
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.ringbuffer_is_full) for ringbuffer_is_full
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.ringbuffer_write) for ringbuffer_write
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.bss) for usart_rb
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for uart_rx_dma_buffer
    uart_app.o(i.uart_proc) refers to ringbuffer.o(i.ringbuffer_is_empty) for ringbuffer_is_empty
    uart_app.o(i.uart_proc) refers to ringbuffer.o(i.ringbuffer_read) for ringbuffer_read
    uart_app.o(i.uart_proc) refers to printfa.o(i.__0printf) for __2printf
    uart_app.o(i.uart_proc) refers to memseta.o(.text) for __aeabi_memclr4
    uart_app.o(i.uart_proc) refers to uart_app.o(.bss) for usart_rb
    ringbuffer.o(i.ringbuffer_init) refers to memseta.o(.text) for __aeabi_memclr4
    ringbuffer.o(i.ringbuffer_read) refers to ringbuffer.o(i.ringbuffer_is_empty) for ringbuffer_is_empty
    ringbuffer.o(i.ringbuffer_write) refers to ringbuffer.o(i.ringbuffer_is_full) for ringbuffer_is_full
    adc_app.o(i.adc_proc) refers to fft_app.o(i.fft_process) for fft_process
    adc_app.o(i.adc_proc) refers to adc_app.o(.data) for input_sum
    adc_app.o(i.adc_proc) refers to adc_app.o(.bss) for dma_buff
    adc_app.o(i.adc_proc) refers to lcd_app.o(.data) for lcd_mode
    adc_app.o(i.adc_proc) refers to lcd_app.o(.bss) for lcd_show_buff
    adc_app.o(i.adc_proc) refers to fft_app.o(.bss) for adc_input_buffer
    filter.o(i.adc_filter) refers to filter.o(.data) for LastValue
    filter.o(i.mid_value) refers to malloc.o(i.malloc) for malloc
    filter.o(i.mid_value) refers to memcpya.o(.text) for __aeabi_memcpy4
    filter.o(i.mid_value) refers to qsort.o(.text) for qsort
    filter.o(i.mid_value) refers to malloc.o(i.free) for free
    filter.o(i.mid_value) refers to filter.o(i.compare) for compare
    i2c_hal.o(i.I2CInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c_hal.o(i.I2CInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c_hal.o(i.I2CReceiveByte) refers to i2c_hal.o(i.SDA_Input_Mode) for SDA_Input_Mode
    i2c_hal.o(i.I2CReceiveByte) refers to i2c_hal.o(i.SCL_Output) for SCL_Output
    i2c_hal.o(i.I2CReceiveByte) refers to i2c_hal.o(i.delay1) for delay1
    i2c_hal.o(i.I2CReceiveByte) refers to i2c_hal.o(i.SDA_Input) for SDA_Input
    i2c_hal.o(i.I2CReceiveByte) refers to i2c_hal.o(i.SDA_Output_Mode) for SDA_Output_Mode
    i2c_hal.o(i.I2CSendAck) refers to i2c_hal.o(i.SDA_Output) for SDA_Output
    i2c_hal.o(i.I2CSendAck) refers to i2c_hal.o(i.delay1) for delay1
    i2c_hal.o(i.I2CSendAck) refers to i2c_hal.o(i.SCL_Output) for SCL_Output
    i2c_hal.o(i.I2CSendByte) refers to i2c_hal.o(i.SCL_Output) for SCL_Output
    i2c_hal.o(i.I2CSendByte) refers to i2c_hal.o(i.delay1) for delay1
    i2c_hal.o(i.I2CSendByte) refers to i2c_hal.o(i.SDA_Output) for SDA_Output
    i2c_hal.o(i.I2CSendNotAck) refers to i2c_hal.o(i.SDA_Output) for SDA_Output
    i2c_hal.o(i.I2CSendNotAck) refers to i2c_hal.o(i.delay1) for delay1
    i2c_hal.o(i.I2CSendNotAck) refers to i2c_hal.o(i.SCL_Output) for SCL_Output
    i2c_hal.o(i.I2CStart) refers to i2c_hal.o(i.SDA_Output) for SDA_Output
    i2c_hal.o(i.I2CStart) refers to i2c_hal.o(i.delay1) for delay1
    i2c_hal.o(i.I2CStart) refers to i2c_hal.o(i.SCL_Output) for SCL_Output
    i2c_hal.o(i.I2CStop) refers to i2c_hal.o(i.SCL_Output) for SCL_Output
    i2c_hal.o(i.I2CStop) refers to i2c_hal.o(i.delay1) for delay1
    i2c_hal.o(i.I2CStop) refers to i2c_hal.o(i.SDA_Output) for SDA_Output
    i2c_hal.o(i.I2CWaitAck) refers to i2c_hal.o(i.SDA_Input_Mode) for SDA_Input_Mode
    i2c_hal.o(i.I2CWaitAck) refers to i2c_hal.o(i.delay1) for delay1
    i2c_hal.o(i.I2CWaitAck) refers to i2c_hal.o(i.SCL_Output) for SCL_Output
    i2c_hal.o(i.I2CWaitAck) refers to i2c_hal.o(i.SDA_Output_Mode) for SDA_Output_Mode
    i2c_hal.o(i.I2CWaitAck) refers to i2c_hal.o(i.I2CStop) for I2CStop
    i2c_hal.o(i.I2CWaitAck) refers to i2c_hal.o(i.SDA_Input) for SDA_Input
    i2c_hal.o(i.SDA_Input) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    i2c_hal.o(i.SDA_Input_Mode) refers to memseta.o(.text) for __aeabi_memclr4
    i2c_hal.o(i.SDA_Input_Mode) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c_hal.o(i.SDA_Output_Mode) refers to memseta.o(.text) for __aeabi_memclr4
    i2c_hal.o(i.SDA_Output_Mode) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c_hal.o(i.eeprom_read) refers to i2c_hal.o(i.I2CStart) for I2CStart
    i2c_hal.o(i.eeprom_read) refers to i2c_hal.o(i.I2CSendByte) for I2CSendByte
    i2c_hal.o(i.eeprom_read) refers to i2c_hal.o(i.I2CWaitAck) for I2CWaitAck
    i2c_hal.o(i.eeprom_read) refers to i2c_hal.o(i.I2CReceiveByte) for I2CReceiveByte
    i2c_hal.o(i.eeprom_read) refers to i2c_hal.o(i.I2CSendAck) for I2CSendAck
    i2c_hal.o(i.eeprom_read) refers to i2c_hal.o(i.I2CSendNotAck) for I2CSendNotAck
    i2c_hal.o(i.eeprom_read) refers to i2c_hal.o(i.I2CStop) for I2CStop
    i2c_hal.o(i.eeprom_write) refers to i2c_hal.o(i.I2CStart) for I2CStart
    i2c_hal.o(i.eeprom_write) refers to i2c_hal.o(i.I2CSendByte) for I2CSendByte
    i2c_hal.o(i.eeprom_write) refers to i2c_hal.o(i.I2CWaitAck) for I2CWaitAck
    i2c_hal.o(i.eeprom_write) refers to i2c_hal.o(i.delay1) for delay1
    i2c_hal.o(i.eeprom_write) refers to i2c_hal.o(i.I2CStop) for I2CStop
    i2c_hal.o(i.eeprom_write) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    i2c_hal.o(i.mcp4017_read) refers to i2c_hal.o(i.I2CStart) for I2CStart
    i2c_hal.o(i.mcp4017_read) refers to i2c_hal.o(i.I2CSendByte) for I2CSendByte
    i2c_hal.o(i.mcp4017_read) refers to i2c_hal.o(i.I2CWaitAck) for I2CWaitAck
    i2c_hal.o(i.mcp4017_read) refers to i2c_hal.o(i.I2CReceiveByte) for I2CReceiveByte
    i2c_hal.o(i.mcp4017_read) refers to i2c_hal.o(i.I2CSendNotAck) for I2CSendNotAck
    i2c_hal.o(i.mcp4017_read) refers to i2c_hal.o(i.I2CStop) for I2CStop
    i2c_hal.o(i.mcp4017_write) refers to i2c_hal.o(i.I2CStart) for I2CStart
    i2c_hal.o(i.mcp4017_write) refers to i2c_hal.o(i.I2CSendByte) for I2CSendByte
    i2c_hal.o(i.mcp4017_write) refers to i2c_hal.o(i.I2CWaitAck) for I2CWaitAck
    i2c_hal.o(i.mcp4017_write) refers to i2c_hal.o(i.I2CStop) for I2CStop
    rtc_app.o(i.rtc_proc) refers to stm32g4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    rtc_app.o(i.rtc_proc) refers to stm32g4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    rtc_app.o(i.rtc_proc) refers to rtc_app.o(.bss) for time
    rtc_app.o(i.rtc_proc) refers to rtc.o(.bss) for hrtc
    rtc_app.o(i.rtc_proc) refers to rtc_app.o(.data) for date
    tim_app.o(i.ic_proc) refers to filter.o(i.limit_value) for limit_value
    tim_app.o(i.ic_proc) refers to tim_app.o(.data) for tim_ic_temp
    tim_app.o(i.ic_proc) refers to tim_app.o(.bss) for tim_ic_buffer
    fft_app.o(i.fft_process) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    fft_app.o(i.fft_process) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    fft_app.o(i.fft_process) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    fft_app.o(i.fft_process) refers to fft_app.o(.bss) for adc_input_buffer
    fft_app.o(i.find_peak_frequency) refers to fft_app.o(.bss) for fft_output
    arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for arm_bitreversal_f32
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for [Anonymous Symbol]
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_4096) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.armBitRevTable) for armBitRevTable
    arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31) refers to arm_bitreversal.o(.text.arm_bitreversal_q31) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15) refers to arm_bitreversal.o(.text.arm_bitreversal_q15) for [Anonymous Symbol]
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32g431xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32g431xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32g431xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32g431xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32g431xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (124 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(.rrx_text), (6 bytes).
    Removing rtc.o(i.HAL_RTC_MspDeInit), (44 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (100 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (60 bytes).
    Removing usart.o(.data), (8 bytes).
    Removing stm32g4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_adc.o(i.ADC_ConversionStop), (284 bytes).
    Removing stm32g4xx_hal_adc.o(i.ADC_Disable), (148 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (824 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit), (452 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_GetError), (6 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_GetState), (6 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_GetValue), (8 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (312 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (258 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Start), (284 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_StartSampling), (18 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT), (468 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Stop), (74 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_StopSampling), (18 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (136 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (86 bytes).
    Removing stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing), (10 bytes).
    Removing stm32g4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels), (48 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (28 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (138 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start), (176 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue), (60 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (40 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue), (58 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode), (44 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (1580 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (62 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (320 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (264 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (348 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (92 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (104 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (260 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (292 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA), (292 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop), (100 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA), (152 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT), (112 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel), (18 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_GetTrigAuto), (10 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing), (10 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_StartConversion), (20 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime), (40 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetCommonPathInternalCh), (12 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState), (22 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetSamplingTimeCommonConfig), (12 bytes).
    Removing stm32g4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DeInit), (60 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAMErase), (28 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAM_WriteProtectionEnable), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchBooster), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchVDD), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchBooster), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchVDD), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (56 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32g4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DisableLSECSS), (24 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (20 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableLSECSS), (24 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (272 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (92 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (36 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (84 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (44 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (20 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (196 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (144 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (112 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (32 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (180 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (24 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (64 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (176 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (1560 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (36 bytes).
    Removing stm32g4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (40 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_Fast), (64 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (140 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (344 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Lock), (32 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (32 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (48 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program), (152 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (148 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Unlock), (48 bytes).
    Removing stm32g4xx_hal_flash.o(.data), (32 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (108 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_BootLockConfig), (60 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetBootLock), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP), (64 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetSecMem), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (56 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig), (124 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig), (60 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_SecMemConfig), (68 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (316 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig), (80 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase), (44 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_DisableDebugger), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableDebugger), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableSecMemProtection), (24 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (268 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (200 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (116 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (176 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (40 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (40 bytes).
    Removing stm32g4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (296 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32g4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (148 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (36 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (36 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (200 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetPending), (44 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (56 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (24 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (228 bytes).
    Removing stm32g4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit), (188 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (328 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Start), (114 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (106 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (86 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (88 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (32 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (32 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (102 bytes).
    Removing stm32g4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (184 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (40 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (80 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (44 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (28 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (668 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (132 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (132 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (84 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM1), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM2), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDStandbyMode), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (32 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (208 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (208 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM1), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM2), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDDeadBattery), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDStandbyMode), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (44 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (60 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (64 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (52 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (128 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Enable), (48 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (68 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rtc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rtc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_AlarmAEventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler), (76 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_DST_Add1Hour), (48 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_DST_ClearStoreOperation), (48 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_DST_ReadStoreOperation), (16 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_DST_SetStoreOperation), (48 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_DST_Sub1Hour), (48 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_DeInit), (232 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_DeactivateAlarm), (272 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_GetAlarm), (200 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_GetState), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_PollForAlarmAEvent), (84 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_SetAlarm), (472 bytes).
    Removing stm32g4xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT), (520 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_AlarmBEventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPRead), (16 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPWrite), (20 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateCalibrationOutPut), (96 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateInternalTamper), (52 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateInternalTimeStamp), (96 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock), (116 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateTamper), (72 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateTimeStamp), (116 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateWakeUpTimer), (172 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_DisableBypassShadow), (96 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_EnableBypassShadow), (96 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_GetTimeStamp), (168 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_GetWakeUpTimer), (16 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_InternalTamper3EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_InternalTamper4EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_InternalTamper5EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_InternalTamper6EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForAlarmBEvent), (84 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForInternalTamperEvent), (64 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamperEvent), (64 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTimeStampEvent), (104 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForWakeUpTimerEvent), (84 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetCalibrationOutPut), (108 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetInternalTamper), (84 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetInternalTamper_IT), (132 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetInternalTimeStamp), (96 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock), (116 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSmoothCalib), (196 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift), (268 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTamper), (172 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTamper_IT), (220 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTimeStamp), (112 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTimeStamp_IT), (164 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer), (204 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT), (252 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper1EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper2EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper3EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperIRQHandler), (144 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampEventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampIRQHandler), (76 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler), (52 bytes).
    Removing stm32g4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start), (144 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (236 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (156 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (50 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (70 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (62 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (660 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (22 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (464 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (464 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (38 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (38 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (96 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (204 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (204 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (564 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (252 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (238 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (304 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (280 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (58 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start), (348 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (424 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (162 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (262 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (238 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (576 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (136 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init), (110 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start), (332 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (656 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (408 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (228 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (328 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (304 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (296 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (96 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (102 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (176 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (200 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (196 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (220 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (656 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (408 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (228 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (328 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (304 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (50 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (108 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (64 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (26 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (14 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt), (26 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigAsymmetricalDeadTime), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (212 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (180 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (208 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (180 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigDeadTime), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigEncoderIndex), (92 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigSlaveModePreload), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableAsymmetricalDeadTime), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableDeadTimePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableEncoderFirstIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableEncoderIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableSlaveModePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisarmBreakInput), (92 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DitheringDisable), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DitheringEnable), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableAsymmetricalDeadTime), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableDeadTimePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableEncoderFirstIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableEncoderIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableSlaveModePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (38 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (72 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (96 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (248 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (204 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (268 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (216 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (78 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (90 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (232 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (536 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (320 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (236 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (236 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OC_ConfigPulseOnCompare), (80 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (118 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (138 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (162 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (232 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (536 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (320 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (236 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (236 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (52 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TISelection), (180 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (34 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (104 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (68 bytes).
    Removing stm32g4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (74 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (74 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (124 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_LIN_Init), (160 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (58 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (58 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (14 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (148 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Abort), (244 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive), (152 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (184 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (126 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (148 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT), (300 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAPause), (128 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAResume), (108 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop), (148 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DeInit), (74 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (88 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (88 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_GetError), (8 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_GetState), (16 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Receive), (300 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (100 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT), (100 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (28 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (184 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (184 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback), (80 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (48 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt), (56 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback), (88 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (46 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT), (146 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN), (356 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT), (142 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN), (356 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT), (272 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT), (70 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN), (98 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT), (66 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN), (92 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (64 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (152 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (50 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode), (88 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (50 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (384 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (102 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (144 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig), (38 bytes).
    Removing system_stm32g4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32g4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32g4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32g4xx.o(i.SystemCoreClockUpdate), (156 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(i.key_state), (32 bytes).
    Removing key_app.o(i.key_task), (232 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.LCD_ClearLine), (40 bytes).
    Removing lcd.o(i.LCD_DisplayOff), (12 bytes).
    Removing lcd.o(i.LCD_DisplayOn), (14 bytes).
    Removing lcd.o(i.LCD_DisplayString), (74 bytes).
    Removing lcd.o(i.LCD_DrawCircle), (256 bytes).
    Removing lcd.o(i.LCD_DrawLine), (92 bytes).
    Removing lcd.o(i.LCD_DrawMonoPict), (84 bytes).
    Removing lcd.o(i.LCD_DrawPicture), (48 bytes).
    Removing lcd.o(i.LCD_DrawRect), (66 bytes).
    Removing lcd.o(i.LCD_PowerOn), (118 bytes).
    Removing lcd.o(i.LCD_ReadRAM), (172 bytes).
    Removing lcd.o(i.LCD_SetDisplayWindow), (88 bytes).
    Removing lcd.o(i.LCD_WindowModeDisable), (28 bytes).
    Removing lcd.o(i.LCD_WriteBMP), (74 bytes).
    Removing lcd_app.o(.rev16_text), (4 bytes).
    Removing lcd_app.o(.revsh_text), (4 bytes).
    Removing lcd_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing system.o(.rev16_text), (4 bytes).
    Removing system.o(.revsh_text), (4 bytes).
    Removing system.o(.rrx_text), (6 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing ringbuffer.o(.rev16_text), (4 bytes).
    Removing ringbuffer.o(.revsh_text), (4 bytes).
    Removing ringbuffer.o(.rrx_text), (6 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rrx_text), (6 bytes).
    Removing filter.o(.rev16_text), (4 bytes).
    Removing filter.o(.revsh_text), (4 bytes).
    Removing filter.o(.rrx_text), (6 bytes).
    Removing filter.o(i.adc_filter), (32 bytes).
    Removing filter.o(i.avg_value), (28 bytes).
    Removing filter.o(i.compare), (10 bytes).
    Removing filter.o(i.mid_value), (132 bytes).
    Removing filter.o(.data), (4 bytes).
    Removing i2c_hal.o(.rev16_text), (4 bytes).
    Removing i2c_hal.o(.revsh_text), (4 bytes).
    Removing i2c_hal.o(.rrx_text), (6 bytes).
    Removing i2c_hal.o(i.I2CInit), (44 bytes).
    Removing i2c_hal.o(i.I2CReceiveByte), (80 bytes).
    Removing i2c_hal.o(i.I2CSendAck), (46 bytes).
    Removing i2c_hal.o(i.I2CSendByte), (78 bytes).
    Removing i2c_hal.o(i.I2CSendNotAck), (46 bytes).
    Removing i2c_hal.o(i.I2CStart), (52 bytes).
    Removing i2c_hal.o(i.I2CStop), (52 bytes).
    Removing i2c_hal.o(i.I2CWaitAck), (78 bytes).
    Removing i2c_hal.o(i.SCL_Output), (40 bytes).
    Removing i2c_hal.o(i.SDA_Input), (24 bytes).
    Removing i2c_hal.o(i.SDA_Input_Mode), (44 bytes).
    Removing i2c_hal.o(i.SDA_Output), (40 bytes).
    Removing i2c_hal.o(i.SDA_Output_Mode), (44 bytes).
    Removing i2c_hal.o(i.delay1), (12 bytes).
    Removing i2c_hal.o(i.eeprom_read), (84 bytes).
    Removing i2c_hal.o(i.eeprom_write), (74 bytes).
    Removing i2c_hal.o(i.mcp4017_read), (34 bytes).
    Removing i2c_hal.o(i.mcp4017_write), (34 bytes).
    Removing rtc_app.o(.rev16_text), (4 bytes).
    Removing rtc_app.o(.revsh_text), (4 bytes).
    Removing rtc_app.o(.rrx_text), (6 bytes).
    Removing tim_app.o(.rev16_text), (4 bytes).
    Removing tim_app.o(.revsh_text), (4 bytes).
    Removing tim_app.o(.rrx_text), (6 bytes).
    Removing tim_app.o(i.pwm_set_duty), (48 bytes).
    Removing tim_app.o(i.pwm_set_frequency), (80 bytes).
    Removing fft_app.o(.rev16_text), (4 bytes).
    Removing fft_app.o(.revsh_text), (4 bytes).
    Removing fft_app.o(.rrx_text), (6 bytes).
    Removing arm_cmplx_mag_f32.o(.text), (0 bytes).
    Removing arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32), (8 bytes).
    Removing arm_cfft_radix4_init_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32), (8 bytes).
    Removing arm_bitreversal.o(.text), (0 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q31), (160 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q15), (112 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable16), (40 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable32), (96 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable128), (416 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable256), (880 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable512), (896 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable1024), (3600 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable2048), (7616 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_32), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_64), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_256), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_1024), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_4096), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_f32), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).

821 unused section(s) (total 943430 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/rtc.c                        0x00000000   Number         0  rtc.o ABSOLUTE
    ../Core/Src/stm32g4xx_hal_msp.c          0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32g4xx_it.c               0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32g4xx.c           0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.c 0x00000000   Number         0  stm32g4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.c 0x00000000   Number         0  stm32g4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rtc.c 0x00000000   Number         0  stm32g4xx_hal_rtc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rtc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rtc_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c 0x00000000   Number         0  stm32g4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c 0x00000000   Number         0  stm32g4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c 0x00000000   Number         0  stm32g4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c 0x00000000   Number         0  stm32g4xx_hal_uart_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_adc.c 0x00000000   Number         0  stm32g4xx_ll_adc.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stdlib/qsort.c          0x00000000   Number         0  qsort.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ..\APP\adc_app.c                         0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\APP\fft_app.c                         0x00000000   Number         0  fft_app.o ABSOLUTE
    ..\APP\filter.c                          0x00000000   Number         0  filter.o ABSOLUTE
    ..\APP\i2c_hal.c                         0x00000000   Number         0  i2c_hal.o ABSOLUTE
    ..\APP\key_app.c                         0x00000000   Number         0  key_app.o ABSOLUTE
    ..\APP\lcd.c                             0x00000000   Number         0  lcd.o ABSOLUTE
    ..\APP\lcd_app.c                         0x00000000   Number         0  lcd_app.o ABSOLUTE
    ..\APP\led_app.c                         0x00000000   Number         0  led_app.o ABSOLUTE
    ..\APP\ringbuffer.c                      0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\APP\rtc_app.c                         0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\APP\system.c                          0x00000000   Number         0  system.o ABSOLUTE
    ..\APP\tim_app.c                         0x00000000   Number         0  tim_app.o ABSOLUTE
    ..\APP\uart_app.c                        0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\rtc.c                        0x00000000   Number         0  rtc.o ABSOLUTE
    ..\Core\Src\stm32g4xx_hal_msp.c          0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32g4xx_it.c               0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32g4xx.c           0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc.c 0x00000000   Number         0  stm32g4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc_ex.c 0x00000000   Number         0  stm32g4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc.c 0x00000000   Number         0  stm32g4xx_hal_rtc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rtc_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim.c 0x00000000   Number         0  stm32g4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim_ex.c 0x00000000   Number         0  stm32g4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart.c 0x00000000   Number         0  stm32g4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart_ex.c 0x00000000   Number         0  stm32g4xx_hal_uart_ex.o ABSOLUTE
    ..\\APP\\adc_app.c                       0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\APP\\fft_app.c                       0x00000000   Number         0  fft_app.o ABSOLUTE
    ..\\APP\\filter.c                        0x00000000   Number         0  filter.o ABSOLUTE
    ..\\APP\\i2c_hal.c                       0x00000000   Number         0  i2c_hal.o ABSOLUTE
    ..\\APP\\key_app.c                       0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\APP\\lcd.c                           0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\APP\\lcd_app.c                       0x00000000   Number         0  lcd_app.o ABSOLUTE
    ..\\APP\\led_app.c                       0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\APP\\ringbuffer.c                    0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\\APP\\rtc_app.c                       0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\APP\\system.c                        0x00000000   Number         0  system.o ABSOLUTE
    ..\\APP\\tim_app.c                       0x00000000   Number         0  tim_app.o ABSOLUTE
    ..\\APP\\uart_app.c                      0x00000000   Number         0  uart_app.o ABSOLUTE
    arm_bitreversal.c                        0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    arm_cfft_radix4_f32.c                    0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    arm_cfft_radix4_init_f32.c               0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    arm_cmplx_mag_f32.c                      0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    arm_common_tables.c                      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32g431xx.s                    0x00000000   Number         0  startup_stm32g431xx.o ABSOLUTE
    RESET                                    0x08000000   Section      472  startup_stm32g431xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001d8   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001d8   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001dc   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001e0   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001e0   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001e0   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080001e8   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001e8   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001e8   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001e8   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001ec   Section       36  startup_stm32g431xx.o(.text)
    $v0                                      0x080001ec   Number         0  startup_stm32g431xx.o(.text)
    .text                                    0x08000210   Section        0  uldiv.o(.text)
    .text                                    0x08000272   Section        0  memseta.o(.text)
    .text                                    0x08000296   Section        0  f2d.o(.text)
    .text                                    0x080002bc   Section        0  uidiv.o(.text)
    .text                                    0x080002e8   Section        0  llshl.o(.text)
    .text                                    0x08000306   Section        0  llushr.o(.text)
    .text                                    0x08000326   Section        0  dadd.o(.text)
    .text                                    0x08000326   Section        0  iusefp.o(.text)
    .text                                    0x08000474   Section        0  dmul.o(.text)
    .text                                    0x08000558   Section        0  ddiv.o(.text)
    .text                                    0x08000636   Section        0  dfixul.o(.text)
    .text                                    0x08000668   Section       48  cdrcmple.o(.text)
    .text                                    0x08000698   Section       36  init.o(.text)
    .text                                    0x080006bc   Section        0  llsshr.o(.text)
    .text                                    0x080006e0   Section        0  depilogue.o(.text)
    [Anonymous Symbol]                       0x0800079c   Section        0  arm_bitreversal.o(.text.arm_bitreversal_f32)
    [Anonymous Symbol]                       0x0800085a   Section        0  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    [Anonymous Symbol]                       0x0800089c   Section        0  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    [Anonymous Symbol]                       0x08000930   Section        0  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    [Anonymous Symbol]                       0x08000a84   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    [Anonymous Symbol]                       0x08000de0   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    i.ADC1_2_IRQHandler                      0x0800115c   Section        0  stm32g4xx_it.o(i.ADC1_2_IRQHandler)
    i.ADC_DMAConvCplt                        0x08001174   Section        0  stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x08001206   Section        0  stm32g4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x08001224   Section        0  stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Enable                             0x08001234   Section        0  stm32g4xx_hal_adc.o(i.ADC_Enable)
    i.BusFault_Handler                       0x080012b8   Section        0  stm32g4xx_it.o(i.BusFault_Handler)
    i.DMA1_Channel1_IRQHandler               0x080012bc   Section        0  stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel2_IRQHandler               0x080012cc   Section        0  stm32g4xx_it.o(i.DMA1_Channel2_IRQHandler)
    i.DMA1_Channel3_IRQHandler               0x080012dc   Section        0  stm32g4xx_it.o(i.DMA1_Channel3_IRQHandler)
    i.DMA1_Channel4_IRQHandler               0x080012ec   Section        0  stm32g4xx_it.o(i.DMA1_Channel4_IRQHandler)
    i.DMA_CalcDMAMUXChannelBaseAndMask       0x080012fc   Section        0  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    DMA_CalcDMAMUXChannelBaseAndMask         0x080012fd   Thumb Code    58  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    i.DMA_CalcDMAMUXRequestGenBaseAndMask    0x08001340   Section        0  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    DMA_CalcDMAMUXRequestGenBaseAndMask      0x08001341   Thumb Code    32  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    i.DMA_SetConfig                          0x08001364   Section        0  stm32g4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08001365   Thumb Code    64  stm32g4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080013a4   Section        0  stm32g4xx_it.o(i.DebugMon_Handler)
    i.Delay_LCD                              0x080013a6   Section        0  lcd.o(i.Delay_LCD)
    i.DisplaySimpleAutoAdjustedWaveform      0x080013c4   Section        0  lcd_app.o(i.DisplaySimpleAutoAdjustedWaveform)
    i.DisplaySpectrum                        0x08001554   Section        0  lcd_app.o(i.DisplaySpectrum)
    i.DisplayWaveform                        0x08001658   Section        0  lcd_app.o(i.DisplayWaveform)
    i.Error_Handler                          0x080016bc   Section        0  main.o(i.Error_Handler)
    i.HAL_ADCEx_EndOfSamplingCallback        0x080016c2   Section        0  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback)
    i.HAL_ADCEx_InjectedConvCpltCallback     0x080016c4   Section        0  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    i.HAL_ADCEx_InjectedQueueOverflowCallback 0x080016c6   Section        0  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback)
    i.HAL_ADCEx_LevelOutOfWindow2Callback    0x080016c8   Section        0  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback)
    i.HAL_ADCEx_LevelOutOfWindow3Callback    0x080016ca   Section        0  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback)
    i.HAL_ADCEx_MultiModeConfigChannel       0x080016cc   Section        0  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel)
    i.HAL_ADC_ConfigChannel                  0x080017d8   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08001ce0   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x08001ce2   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08001ce4   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_IRQHandler                     0x08001ce8   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    i.HAL_ADC_Init                           0x08002018   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_LevelOutOfWindowCallback       0x08002280   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    i.HAL_ADC_MspInit                        0x08002284   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08002448   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_DMA_Abort                          0x0800255c   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080025d2   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002666   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002764   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002834   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x080028f8   Section        0  stm32g4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08002920   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08002af8   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08002b08   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08002b14   Section        0  stm32g4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08002b20   Section        0  stm32g4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002b38   Section        0  stm32g4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002b58   Section        0  stm32g4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002bb0   Section        0  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002bf4   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002c1c   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002c98   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ControlVoltageScaling        0x08002cc0   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_PWREx_DisableUCPDDeadBattery       0x08002dd8   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    i.HAL_RCCEx_PeriphCLKConfig              0x08002dec   Section        0  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x0800315c   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08003380   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x0800338c   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080033b0   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080033d4   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800346c   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_RTC_GetDate                        0x080039d4   Section        0  stm32g4xx_hal_rtc.o(i.HAL_RTC_GetDate)
    i.HAL_RTC_GetTime                        0x08003a24   Section        0  stm32g4xx_hal_rtc.o(i.HAL_RTC_GetTime)
    i.HAL_RTC_Init                           0x08003a88   Section        0  stm32g4xx_hal_rtc.o(i.HAL_RTC_Init)
    i.HAL_RTC_MspInit                        0x08003b3c   Section        0  rtc.o(i.HAL_RTC_MspInit)
    i.HAL_RTC_SetDate                        0x08003ba0   Section        0  stm32g4xx_hal_rtc.o(i.HAL_RTC_SetDate)
    i.HAL_RTC_SetTime                        0x08003c70   Section        0  stm32g4xx_hal_rtc.o(i.HAL_RTC_SetTime)
    i.HAL_RTC_WaitForSynchro                 0x08003d78   Section        0  stm32g4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro)
    i.HAL_SYSTICK_Config                     0x08003db4   Section        0  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08003de8   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003ed0   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08003f88   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003ff8   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_ConfigClockSource              0x08004100   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_ErrorCallback                  0x08004230   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback)
    i.HAL_TIM_IC_CaptureCallback             0x08004232   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IC_CaptureHalfCpltCallback     0x08004234   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback)
    i.HAL_TIM_IC_ConfigChannel               0x08004236   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    i.HAL_TIM_IC_Init                        0x0800430a   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    i.HAL_TIM_IC_MspInit                     0x08004378   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    i.HAL_TIM_IC_Start_DMA                   0x0800437c   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA)
    i.HAL_TIM_MspPostInit                    0x080045f4   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_PWM_ConfigChannel              0x08004690   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x080047f8   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08004866   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_Start                      0x08004868   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_SlaveConfigSynchro             0x080049b4   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro)
    i.HAL_UARTEx_DisableFifoMode             0x08004a20   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08004a6e   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08004ad4   Section        0  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UARTEx_RxFifoFullCallback          0x08004b00   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    i.HAL_UARTEx_SetRxFifoThreshold          0x08004b02   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    i.HAL_UARTEx_SetTxFifoThreshold          0x08004b60   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    i.HAL_UARTEx_TxFifoEmptyCallback         0x08004bbe   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    i.HAL_UARTEx_WakeupCallback              0x08004bc0   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_ErrorCallback                 0x08004bc2   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08004bc4   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004ecc   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004f44   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08005028   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x0800502a   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x0800502c   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08005102   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08005104   Section        0  stm32g4xx_it.o(i.HardFault_Handler)
    i.LCD_BusIn                              0x08005108   Section        0  lcd.o(i.LCD_BusIn)
    i.LCD_BusOut                             0x08005138   Section        0  lcd.o(i.LCD_BusOut)
    i.LCD_Clear                              0x08005168   Section        0  lcd.o(i.LCD_Clear)
    i.LCD_ClearRegion                        0x080051d8   Section        0  lcd.o(i.LCD_ClearRegion)
    i.LCD_CtrlLinesConfig                    0x0800526c   Section        0  lcd.o(i.LCD_CtrlLinesConfig)
    i.LCD_DisplayChar                        0x0800532c   Section        0  lcd.o(i.LCD_DisplayChar)
    i.LCD_DisplayStringLine                  0x08005354   Section        0  lcd.o(i.LCD_DisplayStringLine)
    i.LCD_DrawChar                           0x08005380   Section        0  lcd.o(i.LCD_DrawChar)
    i.LCD_DrawLineAny                        0x08005478   Section        0  lcd.o(i.LCD_DrawLineAny)
    i.LCD_Init                               0x08005510   Section        0  lcd.o(i.LCD_Init)
    i.LCD_ReadReg                            0x08005548   Section        0  lcd.o(i.LCD_ReadReg)
    i.LCD_SetBackColor                       0x080055f4   Section        0  lcd.o(i.LCD_SetBackColor)
    i.LCD_SetCursor                          0x08005604   Section        0  lcd.o(i.LCD_SetCursor)
    i.LCD_SetTextColor                       0x0800561c   Section        0  lcd.o(i.LCD_SetTextColor)
    i.LCD_WR_REG                             0x0800562c   Section        0  lcd.o(i.LCD_WR_REG)
    i.LCD_WriteRAM                           0x08005674   Section        0  lcd.o(i.LCD_WriteRAM)
    i.LCD_WriteRAM_Prepare                   0x080056c4   Section        0  lcd.o(i.LCD_WriteRAM_Prepare)
    i.LCD_WriteReg                           0x080056d0   Section        0  lcd.o(i.LCD_WriteReg)
    i.LL_ADC_Enable                          0x0800572c   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_Enable)
    LL_ADC_Enable                            0x0800572d   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_Enable)
    i.LL_ADC_GetMultimode                    0x0800573c   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode)
    LL_ADC_GetMultimode                      0x0800573d   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode)
    i.LL_ADC_GetOffsetChannel                0x08005746   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    LL_ADC_GetOffsetChannel                  0x08005747   Thumb Code    18  stm32g4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    i.LL_ADC_INJ_IsConversionOngoing         0x08005758   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    LL_ADC_INJ_IsConversionOngoing           0x08005759   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    i.LL_ADC_IsEnabled                       0x08005762   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled)
    LL_ADC_IsEnabled                         0x08005763   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled)
    i.LL_ADC_IsEnabled                       0x0800576c   Section        0  stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled)
    LL_ADC_IsEnabled                         0x0800576d   Thumb Code    10  stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled)
    i.LL_ADC_IsInternalRegulatorEnabled      0x08005776   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled)
    LL_ADC_IsInternalRegulatorEnabled        0x08005777   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled)
    i.LL_ADC_REG_IsConversionOngoing         0x08005780   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x08005781   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsConversionOngoing         0x0800578a   Section        0  stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x0800578b   Thumb Code    10  stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsTriggerSourceSWStart      0x08005794   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    LL_ADC_REG_IsTriggerSourceSWStart        0x08005795   Thumb Code    18  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    i.LL_ADC_REG_StartConversion             0x080057a8   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion)
    LL_ADC_REG_StartConversion               0x080057a9   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion)
    i.LL_ADC_SetChannelSamplingTime          0x080057b8   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    LL_ADC_SetChannelSamplingTime            0x080057b9   Thumb Code    40  stm32g4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    i.LL_ADC_SetCommonPathInternalCh         0x080057e0   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh)
    LL_ADC_SetCommonPathInternalCh           0x080057e1   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh)
    i.LL_ADC_SetOffsetState                  0x080057ec   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    LL_ADC_SetOffsetState                    0x080057ed   Thumb Code    22  stm32g4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    i.LL_ADC_SetSamplingTimeCommonConfig     0x08005802   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonConfig)
    LL_ADC_SetSamplingTimeCommonConfig       0x08005803   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonConfig)
    i.LcdSprintf                             0x0800580e   Section        0  lcd_app.o(i.LcdSprintf)
    i.MX_ADC1_Init                           0x08005838   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_ADC2_Init                           0x080058dc   Section        0  adc.o(i.MX_ADC2_Init)
    i.MX_DMA_Init                            0x08005968   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x080059e8   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_RTC_Init                            0x08005b68   Section        0  rtc.o(i.MX_RTC_Init)
    i.MX_TIM1_Init                           0x08005c04   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08005cf4   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08005d98   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_USART1_UART_Init                    0x08005e60   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08005eec   Section        0  stm32g4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08005ef0   Section        0  stm32g4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08005ef4   Section        0  stm32g4xx_it.o(i.PendSV_Handler)
    i.RCC_GetSysClockFreqFromPLLSource       0x08005ef8   Section        0  stm32g4xx_hal_rcc.o(i.RCC_GetSysClockFreqFromPLLSource)
    RCC_GetSysClockFreqFromPLLSource         0x08005ef9   Thumb Code    90  stm32g4xx_hal_rcc.o(i.RCC_GetSysClockFreqFromPLLSource)
    i.REG_8230_Init                          0x08005f60   Section        0  lcd.o(i.REG_8230_Init)
    i.REG_932X_Init                          0x08006080   Section        0  lcd.o(i.REG_932X_Init)
    i.RTC_Bcd2ToByte                         0x08006280   Section        0  stm32g4xx_hal_rtc.o(i.RTC_Bcd2ToByte)
    i.RTC_ByteToBcd2                         0x08006294   Section        0  stm32g4xx_hal_rtc.o(i.RTC_ByteToBcd2)
    i.RTC_EnterInitMode                      0x080062b0   Section        0  stm32g4xx_hal_rtc.o(i.RTC_EnterInitMode)
    i.RTC_ExitInitMode                       0x08006300   Section        0  stm32g4xx_hal_rtc.o(i.RTC_ExitInitMode)
    i.SVC_Handler                            0x08006368   Section        0  stm32g4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x0800636a   Section        0  stm32g4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08006372   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080063e4   Section        0  system_stm32g4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x080063f8   Section        0  stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x080064b4   Section        0  stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_DMACaptureCplt                     0x080064d6   Section        0  stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt)
    i.TIM_DMACaptureHalfCplt                 0x08006550   Section        0  stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt)
    i.TIM_DMAError                           0x08006590   Section        0  stm32g4xx_hal_tim.o(i.TIM_DMAError)
    i.TIM_ETR_SetConfig                      0x080065ee   Section        0  stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08006604   Section        0  stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08006605   Thumb Code    18  stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x0800661c   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x0800661d   Thumb Code   146  stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x080066c8   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x0800677c   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x0800677d   Thumb Code   154  stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08006830   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08006831   Thumb Code   156  stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_OC5_SetConfig                      0x080068e4   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    TIM_OC5_SetConfig                        0x080068e5   Thumb Code    86  stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    i.TIM_OC6_SetConfig                      0x08006954   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    TIM_OC6_SetConfig                        0x08006955   Thumb Code    88  stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    i.TIM_SlaveTimer_SetConfig               0x080069c4   Section        0  stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig)
    TIM_SlaveTimer_SetConfig                 0x080069c5   Thumb Code   240  stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08006ac0   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08006ac1   Thumb Code    38  stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI1_SetConfig                      0x08006ae8   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    i.TIM_TI2_ConfigInputStage               0x08006b64   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08006b65   Thumb Code    40  stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.TIM_TI2_SetConfig                      0x08006b8c   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    TIM_TI2_SetConfig                        0x08006b8d   Thumb Code    58  stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    i.TIM_TI3_SetConfig                      0x08006bc6   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI3_SetConfig)
    TIM_TI3_SetConfig                        0x08006bc7   Thumb Code    56  stm32g4xx_hal_tim.o(i.TIM_TI3_SetConfig)
    i.TIM_TI4_SetConfig                      0x08006bfe   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI4_SetConfig)
    TIM_TI4_SetConfig                        0x08006bff   Thumb Code    60  stm32g4xx_hal_tim.o(i.TIM_TI4_SetConfig)
    i.UARTEx_SetNbDataToProcess              0x08006c3c   Section        0  stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    UARTEx_SetNbDataToProcess                0x08006c3d   Thumb Code    78  stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    i.UART_AdvFeatureConfig                  0x08006c94   Section        0  stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x08006d8c   Section        0  stm32g4xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x08006e02   Section        0  stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08006e03   Thumb Code    24  stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08006e1a   Section        0  stm32g4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08006e1b   Thumb Code    94  stm32g4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08006e78   Section        0  stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08006e79   Thumb Code   108  stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08006ee4   Section        0  stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08006ee5   Thumb Code    34  stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08006f08   Section        0  stm32g4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08006f09   Thumb Code    56  stm32g4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08006f44   Section        0  stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08006f45   Thumb Code    34  stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08006f66   Section        0  stm32g4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08006f67   Thumb Code    32  stm32g4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_SetConfig                         0x08006f88   Section        0  stm32g4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08007314   Section        0  stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_WaitOnFlagUntilTimeout            0x080073b4   Section        0  stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08007474   Section        0  stm32g4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x080074a4   Section        0  stm32g4xx_it.o(i.UsageFault_Handler)
    i.__0printf                              0x080074a8   Section        0  printfa.o(i.__0printf)
    i.__0vsprintf                            0x080074c8   Section        0  printfa.o(i.__0vsprintf)
    i.__NVIC_GetPriorityGrouping             0x080074ec   Section        0  stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x080074ed   Thumb Code    10  stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x080074fc   Section        0  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080074fd   Thumb Code    32  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_sqrtf                         0x08007524   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__scatterload_copy                     0x0800755e   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800756c   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800756e   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x0800757c   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x08007588   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08007589   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x0800770c   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0800770d   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08007de8   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08007de9   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08007e0c   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08007e0d   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x08007e3a   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08007e3b   Thumb Code    10  printfa.o(i._sputc)
    i.adc_proc                               0x08007e44   Section        0  adc_app.o(i.adc_proc)
    i.calculate_duty_cycle                   0x08007f78   Section        0  lcd_app.o(i.calculate_duty_cycle)
    i.cusum_detect                           0x08007fc0   Section        0  lcd_app.o(i.cusum_detect)
    i.cusum_detect_duty                      0x08007ff4   Section        0  lcd_app.o(i.cusum_detect_duty)
    i.fft_process                            0x08008028   Section        0  fft_app.o(i.fft_process)
    i.find_peak_frequency                    0x08008094   Section        0  fft_app.o(i.find_peak_frequency)
    i.fputc                                  0x080080f4   Section        0  usart.o(i.fputc)
    i.ic_proc                                0x0800810c   Section        0  tim_app.o(i.ic_proc)
    i.key_init                               0x08008174   Section        0  key_app.o(i.key_init)
    i.key_proc                               0x080081d0   Section        0  key_app.o(i.key_proc)
    i.key_read                               0x08008284   Section        0  key_app.o(i.key_read)
    i.lcd_proc                               0x080082c4   Section        0  lcd_app.o(i.lcd_proc)
    i.led_disp                               0x080086b0   Section        0  led_app.o(i.led_disp)
    i.led_proc                               0x0800871c   Section        0  led_app.o(i.led_proc)
    i.limit_value                            0x0800872c   Section        0  filter.o(i.limit_value)
    i.main                                   0x08008758   Section        0  main.o(i.main)
    i.map                                    0x080087fc   Section        0  lcd_app.o(i.map)
    i.my_LCD_DrawLine                        0x08008812   Section        0  lcd.o(i.my_LCD_DrawLine)
    i.ringbuffer_init                        0x0800885e   Section        0  ringbuffer.o(i.ringbuffer_init)
    i.ringbuffer_is_empty                    0x08008878   Section        0  ringbuffer.o(i.ringbuffer_is_empty)
    i.ringbuffer_is_full                     0x08008886   Section        0  ringbuffer.o(i.ringbuffer_is_full)
    i.ringbuffer_read                        0x08008896   Section        0  ringbuffer.o(i.ringbuffer_read)
    i.ringbuffer_write                       0x080088da   Section        0  ringbuffer.o(i.ringbuffer_write)
    i.rtc_proc                               0x08008920   Section        0  rtc_app.o(i.rtc_proc)
    i.scheduler_init                         0x08008944   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08008950   Section        0  scheduler.o(i.scheduler_run)
    i.system_init                            0x080089a8   Section        0  system.o(i.system_init)
    i.uart_proc                              0x080089e4   Section        0  uart_app.o(i.uart_proc)
    .constdata                               0x08008a30   Section       24  stm32g4xx_hal_uart.o(.constdata)
    .constdata                               0x08008a48   Section       16  stm32g4xx_hal_uart_ex.o(.constdata)
    numerator                                0x08008a48   Data           8  stm32g4xx_hal_uart_ex.o(.constdata)
    denominator                              0x08008a50   Data           8  stm32g4xx_hal_uart_ex.o(.constdata)
    .constdata                               0x08008a58   Section       24  system_stm32g4xx.o(.constdata)
    .constdata                               0x08008a70   Section     4560  lcd.o(.constdata)
    .data                                    0x20000000   Section        4  adc.o(.data)
    HAL_RCC_ADC12_CLK_ENABLED                0x20000000   Data           4  adc.o(.data)
    .data                                    0x20000004   Section       12  stm32g4xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32g4xx.o(.data)
    .data                                    0x20000014   Section       88  scheduler.o(.data)
    scheduler_task                           0x20000018   Data          84  scheduler.o(.data)
    .data                                    0x2000006c   Section        4  key_app.o(.data)
    .data                                    0x20000070   Section        6  lcd.o(.data)
    TextColor                                0x20000070   Data           2  lcd.o(.data)
    BackColor                                0x20000072   Data           2  lcd.o(.data)
    .data                                    0x20000078   Section       44  lcd_app.o(.data)
    cumulative_sum                           0x2000009c   Data           4  lcd_app.o(.data)
    cumulative_sum                           0x200000a0   Data           4  lcd_app.o(.data)
    .data                                    0x200000a4   Section        9  led_app.o(.data)
    temp_old                                 0x200000ac   Data           1  led_app.o(.data)
    .data                                    0x200000b0   Section       37  adc_app.o(.data)
    .data                                    0x200000d5   Section        4  rtc_app.o(.data)
    .data                                    0x200000dc   Section        8  tim_app.o(.data)
    .data                                    0x200000e4   Section        4  stdout.o(.data)
    .data                                    0x200000e8   Section        4  errno.o(.data)
    _errno                                   0x200000e8   Data           4  errno.o(.data)
    .bss                                     0x200000ec   Section      408  adc.o(.bss)
    .bss                                     0x20000284   Section       40  rtc.o(.bss)
    .bss                                     0x200002ac   Section      324  tim.o(.bss)
    .bss                                     0x200003f0   Section      496  usart.o(.bss)
    .bss                                     0x200005e0   Section       64  key_app.o(.bss)
    .bss                                     0x20000620   Section     4096  lcd_app.o(.bss)
    .bss                                     0x20001620   Section      108  uart_app.o(.bss)
    .bss                                     0x2000168c   Section    12288  adc_app.o(.bss)
    .bss                                     0x2000468c   Section       20  rtc_app.o(.bss)
    .bss                                     0x200046a0   Section      256  tim_app.o(.bss)
    .bss                                     0x200047a0   Section    12308  fft_app.o(.bss)
    STACK                                    0x200077b8   Section     1024  startup_stm32g431xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001d8   Number         0  startup_stm32g431xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32g431xx.o(RESET)
    __Vectors_End                            0x080001d8   Data           0  startup_stm32g431xx.o(RESET)
    __main                                   0x080001d9   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001d9   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001dd   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001e1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001e1   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001e1   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001e1   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001e9   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001e9   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001ed   Thumb Code     8  startup_stm32g431xx.o(.text)
    COMP1_2_3_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    COMP4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    CORDIC_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    CRS_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMAMUX_OVR_IRQHandler                    0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI0_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI1_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI2_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI3_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FLASH_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FMAC_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FPU_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPTIM1_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPUART1_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    PVD_PVM_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RCC_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RNG_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_TAMP_LSECSS_IRQHandler               0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SAI1_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI1_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI2_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI3_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_BRK_TIM15_IRQHandler                0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_TRG_COM_TIM17_IRQHandler            0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_UP_TIM16_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM2_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM3_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM4_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM7_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_BRK_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_UP_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    UART4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    UCPD1_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART2_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART3_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USBWakeUp_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_HP_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_LP_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    WWDG_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    __aeabi_uldivmod                         0x08000211   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08000273   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000273   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000273   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000281   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000281   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000281   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000285   Thumb Code    18  memseta.o(.text)
    __aeabi_f2d                              0x08000297   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x080002bd   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080002bd   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080002e9   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080002e9   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000307   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000307   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000327   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x08000327   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000469   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800046f   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000475   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000559   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08000637   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000669   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000699   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000699   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x080006bd   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006bd   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x080006e1   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080006ff   Thumb Code   156  depilogue.o(.text)
    arm_bitreversal_f32                      0x0800079d   Thumb Code   190  arm_bitreversal.o(.text.arm_bitreversal_f32)
    arm_cfft_radix4_f32                      0x0800085b   Thumb Code    64  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    arm_cfft_radix4_init_f32                 0x0800089d   Thumb Code   148  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    arm_cmplx_mag_f32                        0x08000931   Thumb Code   340  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    arm_radix4_butterfly_f32                 0x08000a85   Thumb Code   858  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    arm_radix4_butterfly_inverse_f32         0x08000de1   Thumb Code   890  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    ADC1_2_IRQHandler                        0x0800115d   Thumb Code    16  stm32g4xx_it.o(i.ADC1_2_IRQHandler)
    ADC_DMAConvCplt                          0x08001175   Thumb Code   146  stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAError                             0x08001207   Thumb Code    30  stm32g4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAHalfConvCplt                      0x08001225   Thumb Code    14  stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_Enable                               0x08001235   Thumb Code   128  stm32g4xx_hal_adc.o(i.ADC_Enable)
    BusFault_Handler                         0x080012b9   Thumb Code     4  stm32g4xx_it.o(i.BusFault_Handler)
    DMA1_Channel1_IRQHandler                 0x080012bd   Thumb Code    10  stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_IRQHandler                 0x080012cd   Thumb Code    10  stm32g4xx_it.o(i.DMA1_Channel2_IRQHandler)
    DMA1_Channel3_IRQHandler                 0x080012dd   Thumb Code    10  stm32g4xx_it.o(i.DMA1_Channel3_IRQHandler)
    DMA1_Channel4_IRQHandler                 0x080012ed   Thumb Code    10  stm32g4xx_it.o(i.DMA1_Channel4_IRQHandler)
    DebugMon_Handler                         0x080013a5   Thumb Code     2  stm32g4xx_it.o(i.DebugMon_Handler)
    Delay_LCD                                0x080013a7   Thumb Code    30  lcd.o(i.Delay_LCD)
    DisplaySimpleAutoAdjustedWaveform        0x080013c5   Thumb Code   400  lcd_app.o(i.DisplaySimpleAutoAdjustedWaveform)
    DisplaySpectrum                          0x08001555   Thumb Code   242  lcd_app.o(i.DisplaySpectrum)
    DisplayWaveform                          0x08001659   Thumb Code   100  lcd_app.o(i.DisplayWaveform)
    Error_Handler                            0x080016bd   Thumb Code     6  main.o(i.Error_Handler)
    HAL_ADCEx_EndOfSamplingCallback          0x080016c3   Thumb Code     2  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback)
    HAL_ADCEx_InjectedConvCpltCallback       0x080016c5   Thumb Code     2  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADCEx_InjectedQueueOverflowCallback  0x080016c7   Thumb Code     2  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback)
    HAL_ADCEx_LevelOutOfWindow2Callback      0x080016c9   Thumb Code     2  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback)
    HAL_ADCEx_LevelOutOfWindow3Callback      0x080016cb   Thumb Code     2  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback)
    HAL_ADCEx_MultiModeConfigChannel         0x080016cd   Thumb Code   260  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel)
    HAL_ADC_ConfigChannel                    0x080017d9   Thumb Code  1252  stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08001ce1   Thumb Code     2  stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08001ce3   Thumb Code     2  stm32g4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x08001ce5   Thumb Code     2  stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_IRQHandler                       0x08001ce9   Thumb Code   808  stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x08002019   Thumb Code   586  stm32g4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x08002281   Thumb Code     2  stm32g4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x08002285   Thumb Code   428  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08002449   Thumb Code   254  stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_DMA_Abort                            0x0800255d   Thumb Code   118  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080025d3   Thumb Code   148  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002667   Thumb Code   254  stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002765   Thumb Code   200  stm32g4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002835   Thumb Code   194  stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x080028f9   Thumb Code    36  stm32g4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08002921   Thumb Code   438  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08002af9   Thumb Code    16  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08002b09   Thumb Code    10  stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002b15   Thumb Code     6  stm32g4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08002b21   Thumb Code    16  stm32g4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002b39   Thumb Code    30  stm32g4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002b59   Thumb Code    74  stm32g4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002bb1   Thumb Code    62  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002bf5   Thumb Code    40  stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002c1d   Thumb Code   122  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002c99   Thumb Code    32  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x08002cc1   Thumb Code   268  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_DisableUCPDDeadBattery         0x08002dd9   Thumb Code    14  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    HAL_RCCEx_PeriphCLKConfig                0x08002ded   Thumb Code   872  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x0800315d   Thumb Code   522  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08003381   Thumb Code     6  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x0800338d   Thumb Code    26  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080033b1   Thumb Code    26  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080033d5   Thumb Code   138  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800346d   Thumb Code  1370  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_RTC_GetDate                          0x080039d5   Thumb Code    70  stm32g4xx_hal_rtc.o(i.HAL_RTC_GetDate)
    HAL_RTC_GetTime                          0x08003a25   Thumb Code    90  stm32g4xx_hal_rtc.o(i.HAL_RTC_GetTime)
    HAL_RTC_Init                             0x08003a89   Thumb Code   172  stm32g4xx_hal_rtc.o(i.HAL_RTC_Init)
    HAL_RTC_MspInit                          0x08003b3d   Thumb Code    90  rtc.o(i.HAL_RTC_MspInit)
    HAL_RTC_SetDate                          0x08003ba1   Thumb Code   198  stm32g4xx_hal_rtc.o(i.HAL_RTC_SetDate)
    HAL_RTC_SetTime                          0x08003c71   Thumb Code   256  stm32g4xx_hal_rtc.o(i.HAL_RTC_SetTime)
    HAL_RTC_WaitForSynchro                   0x08003d79   Thumb Code    56  stm32g4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro)
    HAL_SYSTICK_Config                       0x08003db5   Thumb Code    52  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x08003de9   Thumb Code   224  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08003ed1   Thumb Code   158  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003f89   Thumb Code   110  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003ff9   Thumb Code   238  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08004101   Thumb Code   296  stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_ErrorCallback                    0x08004231   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback)
    HAL_TIM_IC_CaptureCallback               0x08004233   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IC_CaptureHalfCpltCallback       0x08004235   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback)
    HAL_TIM_IC_ConfigChannel                 0x08004237   Thumb Code   212  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    HAL_TIM_IC_Init                          0x0800430b   Thumb Code   110  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    HAL_TIM_IC_MspInit                       0x08004379   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    HAL_TIM_IC_Start_DMA                     0x0800437d   Thumb Code   596  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA)
    HAL_TIM_MspPostInit                      0x080045f5   Thumb Code   144  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_PWM_ConfigChannel                0x08004691   Thumb Code   360  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x080047f9   Thumb Code   110  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08004867   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_Start                        0x08004869   Thumb Code   298  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_SlaveConfigSynchro               0x080049b5   Thumb Code   108  stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro)
    HAL_UARTEx_DisableFifoMode               0x08004a21   Thumb Code    78  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08004a6f   Thumb Code   102  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08004ad5   Thumb Code    34  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_RxFifoFullCallback            0x08004b01   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    HAL_UARTEx_SetRxFifoThreshold            0x08004b03   Thumb Code    94  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    HAL_UARTEx_SetTxFifoThreshold            0x08004b61   Thumb Code    94  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    HAL_UARTEx_TxFifoEmptyCallback           0x08004bbf   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    HAL_UARTEx_WakeupCallback                0x08004bc1   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x08004bc3   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08004bc5   Thumb Code   762  stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004ecd   Thumb Code   120  stm32g4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004f45   Thumb Code   210  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08005029   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x0800502b   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x0800502d   Thumb Code   214  stm32g4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08005103   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08005105   Thumb Code     4  stm32g4xx_it.o(i.HardFault_Handler)
    LCD_BusIn                                0x08005109   Thumb Code    42  lcd.o(i.LCD_BusIn)
    LCD_BusOut                               0x08005139   Thumb Code    42  lcd.o(i.LCD_BusOut)
    LCD_Clear                                0x08005169   Thumb Code   102  lcd.o(i.LCD_Clear)
    LCD_ClearRegion                          0x080051d9   Thumb Code   140  lcd.o(i.LCD_ClearRegion)
    LCD_CtrlLinesConfig                      0x0800526d   Thumb Code   182  lcd.o(i.LCD_CtrlLinesConfig)
    LCD_DisplayChar                          0x0800532d   Thumb Code    34  lcd.o(i.LCD_DisplayChar)
    LCD_DisplayStringLine                    0x08005355   Thumb Code    44  lcd.o(i.LCD_DisplayStringLine)
    LCD_DrawChar                             0x08005381   Thumb Code   232  lcd.o(i.LCD_DrawChar)
    LCD_DrawLineAny                          0x08005479   Thumb Code   148  lcd.o(i.LCD_DrawLineAny)
    LCD_Init                                 0x08005511   Thumb Code    50  lcd.o(i.LCD_Init)
    LCD_ReadReg                              0x08005549   Thumb Code   164  lcd.o(i.LCD_ReadReg)
    LCD_SetBackColor                         0x080055f5   Thumb Code    12  lcd.o(i.LCD_SetBackColor)
    LCD_SetCursor                            0x08005605   Thumb Code    24  lcd.o(i.LCD_SetCursor)
    LCD_SetTextColor                         0x0800561d   Thumb Code    12  lcd.o(i.LCD_SetTextColor)
    LCD_WR_REG                               0x0800562d   Thumb Code    64  lcd.o(i.LCD_WR_REG)
    LCD_WriteRAM                             0x08005675   Thumb Code    72  lcd.o(i.LCD_WriteRAM)
    LCD_WriteRAM_Prepare                     0x080056c5   Thumb Code    10  lcd.o(i.LCD_WriteRAM_Prepare)
    LCD_WriteReg                             0x080056d1   Thumb Code    84  lcd.o(i.LCD_WriteReg)
    LcdSprintf                               0x0800580f   Thumb Code    42  lcd_app.o(i.LcdSprintf)
    MX_ADC1_Init                             0x08005839   Thumb Code   154  adc.o(i.MX_ADC1_Init)
    MX_ADC2_Init                             0x080058dd   Thumb Code   126  adc.o(i.MX_ADC2_Init)
    MX_DMA_Init                              0x08005969   Thumb Code   122  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x080059e9   Thumb Code   366  gpio.o(i.MX_GPIO_Init)
    MX_RTC_Init                              0x08005b69   Thumb Code   146  rtc.o(i.MX_RTC_Init)
    MX_TIM1_Init                             0x08005c05   Thumb Code   230  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08005cf5   Thumb Code   160  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08005d99   Thumb Code   190  tim.o(i.MX_TIM3_Init)
    MX_USART1_UART_Init                      0x08005e61   Thumb Code   124  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08005eed   Thumb Code     4  stm32g4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08005ef1   Thumb Code     4  stm32g4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08005ef5   Thumb Code     2  stm32g4xx_it.o(i.PendSV_Handler)
    REG_8230_Init                            0x08005f61   Thumb Code   288  lcd.o(i.REG_8230_Init)
    REG_932X_Init                            0x08006081   Thumb Code   512  lcd.o(i.REG_932X_Init)
    RTC_Bcd2ToByte                           0x08006281   Thumb Code    20  stm32g4xx_hal_rtc.o(i.RTC_Bcd2ToByte)
    RTC_ByteToBcd2                           0x08006295   Thumb Code    28  stm32g4xx_hal_rtc.o(i.RTC_ByteToBcd2)
    RTC_EnterInitMode                        0x080062b1   Thumb Code    74  stm32g4xx_hal_rtc.o(i.RTC_EnterInitMode)
    RTC_ExitInitMode                         0x08006301   Thumb Code   100  stm32g4xx_hal_rtc.o(i.RTC_ExitInitMode)
    SVC_Handler                              0x08006369   Thumb Code     2  stm32g4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x0800636b   Thumb Code     8  stm32g4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08006373   Thumb Code   114  main.o(i.SystemClock_Config)
    SystemInit                               0x080063e5   Thumb Code    14  system_stm32g4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x080063f9   Thumb Code   158  stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x080064b5   Thumb Code    34  stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_DMACaptureCplt                       0x080064d7   Thumb Code   122  stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt)
    TIM_DMACaptureHalfCplt                   0x08006551   Thumb Code    64  stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt)
    TIM_DMAError                             0x08006591   Thumb Code    94  stm32g4xx_hal_tim.o(i.TIM_DMAError)
    TIM_ETR_SetConfig                        0x080065ef   Thumb Code    22  stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x080066c9   Thumb Code   156  stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    TIM_TI1_SetConfig                        0x08006ae9   Thumb Code   102  stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    UART_AdvFeatureConfig                    0x08006c95   Thumb Code   248  stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x08006d8d   Thumb Code   118  stm32g4xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08006f89   Thumb Code   866  stm32g4xx_hal_uart.o(i.UART_SetConfig)
    UART_Start_Receive_DMA                   0x08007315   Thumb Code   146  stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_WaitOnFlagUntilTimeout              0x080073b5   Thumb Code   190  stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x08007475   Thumb Code    36  stm32g4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x080074a5   Thumb Code     4  stm32g4xx_it.o(i.UsageFault_Handler)
    __0printf                                0x080074a9   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x080074a9   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x080074a9   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x080074a9   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x080074a9   Thumb Code     0  printfa.o(i.__0printf)
    __0vsprintf                              0x080074c9   Thumb Code    30  printfa.o(i.__0vsprintf)
    __1vsprintf                              0x080074c9   Thumb Code     0  printfa.o(i.__0vsprintf)
    __2vsprintf                              0x080074c9   Thumb Code     0  printfa.o(i.__0vsprintf)
    __c89vsprintf                            0x080074c9   Thumb Code     0  printfa.o(i.__0vsprintf)
    vsprintf                                 0x080074c9   Thumb Code     0  printfa.o(i.__0vsprintf)
    __hardfp_sqrtf                           0x08007525   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __scatterload_copy                       0x0800755f   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800756d   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800756f   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x0800757d   Thumb Code     6  errno.o(i.__set_errno)
    adc_proc                                 0x08007e45   Thumb Code   250  adc_app.o(i.adc_proc)
    calculate_duty_cycle                     0x08007f79   Thumb Code    64  lcd_app.o(i.calculate_duty_cycle)
    cusum_detect                             0x08007fc1   Thumb Code    48  lcd_app.o(i.cusum_detect)
    cusum_detect_duty                        0x08007ff5   Thumb Code    48  lcd_app.o(i.cusum_detect_duty)
    fft_process                              0x08008029   Thumb Code    90  fft_app.o(i.fft_process)
    find_peak_frequency                      0x08008095   Thumb Code    82  fft_app.o(i.find_peak_frequency)
    fputc                                    0x080080f5   Thumb Code    20  usart.o(i.fputc)
    ic_proc                                  0x0800810d   Thumb Code    88  tim_app.o(i.ic_proc)
    key_init                                 0x08008175   Thumb Code    82  key_app.o(i.key_init)
    key_proc                                 0x080081d1   Thumb Code   142  key_app.o(i.key_proc)
    key_read                                 0x08008285   Thumb Code    58  key_app.o(i.key_read)
    lcd_proc                                 0x080082c5   Thumb Code   778  lcd_app.o(i.lcd_proc)
    led_disp                                 0x080086b1   Thumb Code    94  led_app.o(i.led_disp)
    led_proc                                 0x0800871d   Thumb Code    10  led_app.o(i.led_proc)
    limit_value                              0x0800872d   Thumb Code    42  filter.o(i.limit_value)
    main                                     0x08008759   Thumb Code   134  main.o(i.main)
    map                                      0x080087fd   Thumb Code    22  lcd_app.o(i.map)
    my_LCD_DrawLine                          0x08008813   Thumb Code    76  lcd.o(i.my_LCD_DrawLine)
    ringbuffer_init                          0x0800885f   Thumb Code    26  ringbuffer.o(i.ringbuffer_init)
    ringbuffer_is_empty                      0x08008879   Thumb Code    14  ringbuffer.o(i.ringbuffer_is_empty)
    ringbuffer_is_full                       0x08008887   Thumb Code    16  ringbuffer.o(i.ringbuffer_is_full)
    ringbuffer_read                          0x08008897   Thumb Code    68  ringbuffer.o(i.ringbuffer_read)
    ringbuffer_write                         0x080088db   Thumb Code    68  ringbuffer.o(i.ringbuffer_write)
    rtc_proc                                 0x08008921   Thumb Code    24  rtc_app.o(i.rtc_proc)
    scheduler_init                           0x08008945   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08008951   Thumb Code    78  scheduler.o(i.scheduler_run)
    system_init                              0x080089a9   Thumb Code    50  system.o(i.system_init)
    uart_proc                                0x080089e5   Thumb Code    42  uart_app.o(i.uart_proc)
    UARTPrescTable                           0x08008a30   Data          24  stm32g4xx_hal_uart.o(.constdata)
    AHBPrescTable                            0x08008a58   Data          16  system_stm32g4xx.o(.constdata)
    APBPrescTable                            0x08008a68   Data           8  system_stm32g4xx.o(.constdata)
    ASCII_Table                              0x08008a70   Data        4560  lcd.o(.constdata)
    armBitRevTable                           0x08009c40   Data        2048  arm_common_tables.o(.rodata.armBitRevTable)
    twiddleCoef_4096                         0x0800a440   Data       32768  arm_common_tables.o(.rodata.twiddleCoef_4096)
    Region$$Table$$Base                      0x08012440   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08012460   Number         0  anon$$obj.o(Region$$Table)
    uwTick                                   0x20000004   Data           4  stm32g4xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32g4xx_hal.o(.data)
    uwTickFreq                               0x2000000c   Data           4  stm32g4xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32g4xx.o(.data)
    task_num                                 0x20000014   Data           1  scheduler.o(.data)
    key_val                                  0x2000006c   Data           1  key_app.o(.data)
    key_old                                  0x2000006d   Data           1  key_app.o(.data)
    key_down                                 0x2000006e   Data           1  key_app.o(.data)
    key_up                                   0x2000006f   Data           1  key_app.o(.data)
    dummy                                    0x20000074   Data           2  lcd.o(.data)
    lcd_change_flag                          0x20000078   Data           1  lcd_app.o(.data)
    lcd_frequence                            0x2000007c   Data           4  lcd_app.o(.data)
    lcd_frequence_old                        0x20000080   Data           4  lcd_app.o(.data)
    lcd_mode                                 0x20000084   Data           1  lcd_app.o(.data)
    auto_flag                                0x20000085   Data           1  lcd_app.o(.data)
    duty                                     0x20000088   Data           4  lcd_app.o(.data)
    show_duty                                0x2000008c   Data           4  lcd_app.o(.data)
    first_count                              0x20000090   Data           1  lcd_app.o(.data)
    target_frequence                         0x20000094   Data           4  lcd_app.o(.data)
    target_duty                              0x20000098   Data           4  lcd_app.o(.data)
    ucLed                                    0x200000a4   Data           8  led_app.o(.data)
    max_number                               0x200000b0   Data           4  adc_app.o(.data)
    min_number                               0x200000b4   Data           4  adc_app.o(.data)
    digital_vpp                              0x200000b8   Data           4  adc_app.o(.data)
    digital_vpp_old                          0x200000bc   Data           4  adc_app.o(.data)
    adc_change                               0x200000c0   Data           1  adc_app.o(.data)
    Vpp                                      0x200000c4   Data           4  adc_app.o(.data)
    input_sum                                0x200000c8   Data           4  adc_app.o(.data)
    input_average                            0x200000cc   Data           4  adc_app.o(.data)
    input_average_old                        0x200000d0   Data           4  adc_app.o(.data)
    input_change_flag                        0x200000d4   Data           1  adc_app.o(.data)
    date                                     0x200000d5   Data           4  rtc_app.o(.data)
    tim_ic_val                               0x200000dc   Data           4  tim_app.o(.data)
    tim_ic_temp                              0x200000e0   Data           4  tim_app.o(.data)
    __stdout                                 0x200000e4   Data           4  stdout.o(.data)
    hadc1                                    0x200000ec   Data         108  adc.o(.bss)
    hadc2                                    0x20000158   Data         108  adc.o(.bss)
    hdma_adc1                                0x200001c4   Data          96  adc.o(.bss)
    hdma_adc2                                0x20000224   Data          96  adc.o(.bss)
    hrtc                                     0x20000284   Data          40  rtc.o(.bss)
    htim1                                    0x200002ac   Data          76  tim.o(.bss)
    htim2                                    0x200002f8   Data          76  tim.o(.bss)
    htim3                                    0x20000344   Data          76  tim.o(.bss)
    hdma_tim3_ch1                            0x20000390   Data          96  tim.o(.bss)
    uart_rx_buffer                           0x200003f0   Data         128  usart.o(.bss)
    uart_rx_dma_buffer                       0x20000470   Data         128  usart.o(.bss)
    huart1                                   0x200004f0   Data         144  usart.o(.bss)
    hdma_usart1_rx                           0x20000580   Data          96  usart.o(.bss)
    btns                                     0x200005e0   Data          64  key_app.o(.bss)
    lcd_show_buff                            0x20000620   Data        4096  lcd_app.o(.bss)
    usart_rb                                 0x20001620   Data          44  uart_app.o(.bss)
    usart_read_buffer                        0x2000164c   Data          64  uart_app.o(.bss)
    dma_buff                                 0x2000168c   Data        4096  adc_app.o(.bss)
    adc_value                                0x2000268c   Data        4096  adc_app.o(.bss)
    dma_buff_t                               0x2000368c   Data        4096  adc_app.o(.bss)
    time                                     0x2000468c   Data          20  rtc_app.o(.bss)
    tim_ic_buffer                            0x200046a0   Data         256  tim_app.o(.bss)
    adc_input_buffer                         0x200047a0   Data        8192  fft_app.o(.bss)
    fft_output                               0x200067a0   Data        4096  fft_app.o(.bss)
    fft_instance                             0x200077a0   Data          20  fft_app.o(.bss)
    __initial_sp                             0x20007bb8   Data           0  startup_stm32g431xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001d9

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0001254c, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00012460, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001d8   Data   RO            3    RESET               startup_stm32g431xx.o
    0x080001d8   0x080001d8   0x00000000   Code   RO         6010  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001d8   0x080001d8   0x00000004   Code   RO         6314    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001dc   0x080001dc   0x00000004   Code   RO         6317    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         6319    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         6321    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001e0   0x080001e0   0x00000008   Code   RO         6322    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001e8   0x080001e8   0x00000000   Code   RO         6324    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001e8   0x080001e8   0x00000000   Code   RO         6326    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001e8   0x080001e8   0x00000004   Code   RO         6315    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001ec   0x080001ec   0x00000024   Code   RO            4    .text               startup_stm32g431xx.o
    0x08000210   0x08000210   0x00000062   Code   RO         6013    .text               mc_w.l(uldiv.o)
    0x08000272   0x08000272   0x00000024   Code   RO         6019    .text               mc_w.l(memseta.o)
    0x08000296   0x08000296   0x00000026   Code   RO         6312    .text               mf_w.l(f2d.o)
    0x080002bc   0x080002bc   0x0000002c   Code   RO         6329    .text               mc_w.l(uidiv.o)
    0x080002e8   0x080002e8   0x0000001e   Code   RO         6331    .text               mc_w.l(llshl.o)
    0x08000306   0x08000306   0x00000020   Code   RO         6333    .text               mc_w.l(llushr.o)
    0x08000326   0x08000326   0x00000000   Code   RO         6344    .text               mc_w.l(iusefp.o)
    0x08000326   0x08000326   0x0000014e   Code   RO         6345    .text               mf_w.l(dadd.o)
    0x08000474   0x08000474   0x000000e4   Code   RO         6347    .text               mf_w.l(dmul.o)
    0x08000558   0x08000558   0x000000de   Code   RO         6349    .text               mf_w.l(ddiv.o)
    0x08000636   0x08000636   0x00000030   Code   RO         6351    .text               mf_w.l(dfixul.o)
    0x08000666   0x08000666   0x00000002   PAD
    0x08000668   0x08000668   0x00000030   Code   RO         6353    .text               mf_w.l(cdrcmple.o)
    0x08000698   0x08000698   0x00000024   Code   RO         6355    .text               mc_w.l(init.o)
    0x080006bc   0x080006bc   0x00000024   Code   RO         6357    .text               mc_w.l(llsshr.o)
    0x080006e0   0x080006e0   0x000000ba   Code   RO         6359    .text               mf_w.l(depilogue.o)
    0x0800079a   0x0800079a   0x00000002   PAD
    0x0800079c   0x0800079c   0x000000be   Code   RO         5865    .text.arm_bitreversal_f32  arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x0800085a   0x0800085a   0x00000040   Code   RO         5842    .text.arm_cfft_radix4_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x0800089a   0x0800089a   0x00000002   PAD
    0x0800089c   0x0800089c   0x00000094   Code   RO         5856    .text.arm_cfft_radix4_init_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x08000930   0x08000930   0x00000154   Code   RO         5832    .text.arm_cmplx_mag_f32  arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x08000a84   0x08000a84   0x0000035a   Code   RO         5846    .text.arm_radix4_butterfly_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08000dde   0x08000dde   0x00000002   PAD
    0x08000de0   0x08000de0   0x0000037a   Code   RO         5844    .text.arm_radix4_butterfly_inverse_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x0800115a   0x0800115a   0x00000002   PAD
    0x0800115c   0x0800115c   0x00000018   Code   RO          515    i.ADC1_2_IRQHandler  stm32g4xx_it.o
    0x08001174   0x08001174   0x00000092   Code   RO          652    i.ADC_DMAConvCplt   stm32g4xx_hal_adc.o
    0x08001206   0x08001206   0x0000001e   Code   RO          653    i.ADC_DMAError      stm32g4xx_hal_adc.o
    0x08001224   0x08001224   0x0000000e   Code   RO          654    i.ADC_DMAHalfConvCplt  stm32g4xx_hal_adc.o
    0x08001232   0x08001232   0x00000002   PAD
    0x08001234   0x08001234   0x00000084   Code   RO          656    i.ADC_Enable        stm32g4xx_hal_adc.o
    0x080012b8   0x080012b8   0x00000004   Code   RO          516    i.BusFault_Handler  stm32g4xx_it.o
    0x080012bc   0x080012bc   0x00000010   Code   RO          517    i.DMA1_Channel1_IRQHandler  stm32g4xx_it.o
    0x080012cc   0x080012cc   0x00000010   Code   RO          518    i.DMA1_Channel2_IRQHandler  stm32g4xx_it.o
    0x080012dc   0x080012dc   0x00000010   Code   RO          519    i.DMA1_Channel3_IRQHandler  stm32g4xx_it.o
    0x080012ec   0x080012ec   0x00000010   Code   RO          520    i.DMA1_Channel4_IRQHandler  stm32g4xx_it.o
    0x080012fc   0x080012fc   0x00000044   Code   RO         2056    i.DMA_CalcDMAMUXChannelBaseAndMask  stm32g4xx_hal_dma.o
    0x08001340   0x08001340   0x00000024   Code   RO         2057    i.DMA_CalcDMAMUXRequestGenBaseAndMask  stm32g4xx_hal_dma.o
    0x08001364   0x08001364   0x00000040   Code   RO         2058    i.DMA_SetConfig     stm32g4xx_hal_dma.o
    0x080013a4   0x080013a4   0x00000002   Code   RO          521    i.DebugMon_Handler  stm32g4xx_it.o
    0x080013a6   0x080013a6   0x0000001e   Code   RO         5039    i.Delay_LCD         lcd.o
    0x080013c4   0x080013c4   0x00000190   Code   RO         5280    i.DisplaySimpleAutoAdjustedWaveform  lcd_app.o
    0x08001554   0x08001554   0x00000104   Code   RO         5281    i.DisplaySpectrum   lcd_app.o
    0x08001658   0x08001658   0x00000064   Code   RO         5282    i.DisplayWaveform   lcd_app.o
    0x080016bc   0x080016bc   0x00000006   Code   RO           13    i.Error_Handler     main.o
    0x080016c2   0x080016c2   0x00000002   Code   RO          933    i.HAL_ADCEx_EndOfSamplingCallback  stm32g4xx_hal_adc_ex.o
    0x080016c4   0x080016c4   0x00000002   Code   RO          936    i.HAL_ADCEx_InjectedConvCpltCallback  stm32g4xx_hal_adc_ex.o
    0x080016c6   0x080016c6   0x00000002   Code   RO          939    i.HAL_ADCEx_InjectedQueueOverflowCallback  stm32g4xx_hal_adc_ex.o
    0x080016c8   0x080016c8   0x00000002   Code   RO          944    i.HAL_ADCEx_LevelOutOfWindow2Callback  stm32g4xx_hal_adc_ex.o
    0x080016ca   0x080016ca   0x00000002   Code   RO          945    i.HAL_ADCEx_LevelOutOfWindow3Callback  stm32g4xx_hal_adc_ex.o
    0x080016cc   0x080016cc   0x0000010c   Code   RO          946    i.HAL_ADCEx_MultiModeConfigChannel  stm32g4xx_hal_adc_ex.o
    0x080017d8   0x080017d8   0x00000508   Code   RO          658    i.HAL_ADC_ConfigChannel  stm32g4xx_hal_adc.o
    0x08001ce0   0x08001ce0   0x00000002   Code   RO          659    i.HAL_ADC_ConvCpltCallback  stm32g4xx_hal_adc.o
    0x08001ce2   0x08001ce2   0x00000002   Code   RO          660    i.HAL_ADC_ConvHalfCpltCallback  stm32g4xx_hal_adc.o
    0x08001ce4   0x08001ce4   0x00000002   Code   RO          662    i.HAL_ADC_ErrorCallback  stm32g4xx_hal_adc.o
    0x08001ce6   0x08001ce6   0x00000002   PAD
    0x08001ce8   0x08001ce8   0x00000330   Code   RO          666    i.HAL_ADC_IRQHandler  stm32g4xx_hal_adc.o
    0x08002018   0x08002018   0x00000268   Code   RO          667    i.HAL_ADC_Init      stm32g4xx_hal_adc.o
    0x08002280   0x08002280   0x00000002   Code   RO          668    i.HAL_ADC_LevelOutOfWindowCallback  stm32g4xx_hal_adc.o
    0x08002282   0x08002282   0x00000002   PAD
    0x08002284   0x08002284   0x000001c4   Code   RO          288    i.HAL_ADC_MspInit   adc.o
    0x08002448   0x08002448   0x00000114   Code   RO          675    i.HAL_ADC_Start_DMA  stm32g4xx_hal_adc.o
    0x0800255c   0x0800255c   0x00000076   Code   RO         2059    i.HAL_DMA_Abort     stm32g4xx_hal_dma.o
    0x080025d2   0x080025d2   0x00000094   Code   RO         2060    i.HAL_DMA_Abort_IT  stm32g4xx_hal_dma.o
    0x08002666   0x08002666   0x000000fe   Code   RO         2064    i.HAL_DMA_IRQHandler  stm32g4xx_hal_dma.o
    0x08002764   0x08002764   0x000000d0   Code   RO         2065    i.HAL_DMA_Init      stm32g4xx_hal_dma.o
    0x08002834   0x08002834   0x000000c2   Code   RO         2069    i.HAL_DMA_Start_IT  stm32g4xx_hal_dma.o
    0x080028f6   0x080028f6   0x00000002   PAD
    0x080028f8   0x080028f8   0x00000028   Code   RO         1170    i.HAL_Delay         stm32g4xx_hal.o
    0x08002920   0x08002920   0x000001d8   Code   RO         1921    i.HAL_GPIO_Init     stm32g4xx_hal_gpio.o
    0x08002af8   0x08002af8   0x00000010   Code   RO         1923    i.HAL_GPIO_ReadPin  stm32g4xx_hal_gpio.o
    0x08002b08   0x08002b08   0x0000000a   Code   RO         1925    i.HAL_GPIO_WritePin  stm32g4xx_hal_gpio.o
    0x08002b12   0x08002b12   0x00000002   PAD
    0x08002b14   0x08002b14   0x0000000c   Code   RO         1174    i.HAL_GetTick       stm32g4xx_hal.o
    0x08002b20   0x08002b20   0x00000018   Code   RO         1177    i.HAL_IncTick       stm32g4xx_hal.o
    0x08002b38   0x08002b38   0x0000001e   Code   RO         1178    i.HAL_Init          stm32g4xx_hal.o
    0x08002b56   0x08002b56   0x00000002   PAD
    0x08002b58   0x08002b58   0x00000058   Code   RO         1179    i.HAL_InitTick      stm32g4xx_hal.o
    0x08002bb0   0x08002bb0   0x00000044   Code   RO          627    i.HAL_MspInit       stm32g4xx_hal_msp.o
    0x08002bf4   0x08002bf4   0x00000028   Code   RO         2568    i.HAL_NVIC_EnableIRQ  stm32g4xx_hal_cortex.o
    0x08002c1c   0x08002c1c   0x0000007a   Code   RO         2574    i.HAL_NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x08002c96   0x08002c96   0x00000002   PAD
    0x08002c98   0x08002c98   0x00000028   Code   RO         2575    i.HAL_NVIC_SetPriorityGrouping  stm32g4xx_hal_cortex.o
    0x08002cc0   0x08002cc0   0x00000118   Code   RO         2324    i.HAL_PWREx_ControlVoltageScaling  stm32g4xx_hal_pwr_ex.o
    0x08002dd8   0x08002dd8   0x00000014   Code   RO         2336    i.HAL_PWREx_DisableUCPDDeadBattery  stm32g4xx_hal_pwr_ex.o
    0x08002dec   0x08002dec   0x00000370   Code   RO         1528    i.HAL_RCCEx_PeriphCLKConfig  stm32g4xx_hal_rcc_ex.o
    0x0800315c   0x0800315c   0x00000224   Code   RO         1398    i.HAL_RCC_ClockConfig  stm32g4xx_hal_rcc.o
    0x08003380   0x08003380   0x0000000c   Code   RO         1404    i.HAL_RCC_GetHCLKFreq  stm32g4xx_hal_rcc.o
    0x0800338c   0x0800338c   0x00000024   Code   RO         1406    i.HAL_RCC_GetPCLK1Freq  stm32g4xx_hal_rcc.o
    0x080033b0   0x080033b0   0x00000024   Code   RO         1407    i.HAL_RCC_GetPCLK2Freq  stm32g4xx_hal_rcc.o
    0x080033d4   0x080033d4   0x00000098   Code   RO         1408    i.HAL_RCC_GetSysClockFreq  stm32g4xx_hal_rcc.o
    0x0800346c   0x0800346c   0x00000568   Code   RO         1411    i.HAL_RCC_OscConfig  stm32g4xx_hal_rcc.o
    0x080039d4   0x080039d4   0x00000050   Code   RO         2713    i.HAL_RTC_GetDate   stm32g4xx_hal_rtc.o
    0x08003a24   0x08003a24   0x00000064   Code   RO         2715    i.HAL_RTC_GetTime   stm32g4xx_hal_rtc.o
    0x08003a88   0x08003a88   0x000000b4   Code   RO         2716    i.HAL_RTC_Init      stm32g4xx_hal_rtc.o
    0x08003b3c   0x08003b3c   0x00000064   Code   RO          361    i.HAL_RTC_MspInit   rtc.o
    0x08003ba0   0x08003ba0   0x000000d0   Code   RO         2722    i.HAL_RTC_SetDate   stm32g4xx_hal_rtc.o
    0x08003c70   0x08003c70   0x00000108   Code   RO         2723    i.HAL_RTC_SetTime   stm32g4xx_hal_rtc.o
    0x08003d78   0x08003d78   0x0000003c   Code   RO         2724    i.HAL_RTC_WaitForSynchro  stm32g4xx_hal_rtc.o
    0x08003db4   0x08003db4   0x00000034   Code   RO         2579    i.HAL_SYSTICK_Config  stm32g4xx_hal_cortex.o
    0x08003de8   0x08003de8   0x000000e8   Code   RO         3891    i.HAL_TIMEx_ConfigBreakDeadTime  stm32g4xx_hal_tim_ex.o
    0x08003ed0   0x08003ed0   0x000000b8   Code   RO         3928    i.HAL_TIMEx_MasterConfigSynchronization  stm32g4xx_hal_tim_ex.o
    0x08003f88   0x08003f88   0x0000006e   Code   RO         3161    i.HAL_TIM_Base_Init  stm32g4xx_hal_tim.o
    0x08003ff6   0x08003ff6   0x00000002   PAD
    0x08003ff8   0x08003ff8   0x00000108   Code   RO          403    i.HAL_TIM_Base_MspInit  tim.o
    0x08004100   0x08004100   0x00000130   Code   RO         3170    i.HAL_TIM_ConfigClockSource  stm32g4xx_hal_tim.o
    0x08004230   0x08004230   0x00000002   Code   RO         3191    i.HAL_TIM_ErrorCallback  stm32g4xx_hal_tim.o
    0x08004232   0x08004232   0x00000002   Code   RO         3195    i.HAL_TIM_IC_CaptureCallback  stm32g4xx_hal_tim.o
    0x08004234   0x08004234   0x00000002   Code   RO         3196    i.HAL_TIM_IC_CaptureHalfCpltCallback  stm32g4xx_hal_tim.o
    0x08004236   0x08004236   0x000000d4   Code   RO         3197    i.HAL_TIM_IC_ConfigChannel  stm32g4xx_hal_tim.o
    0x0800430a   0x0800430a   0x0000006e   Code   RO         3200    i.HAL_TIM_IC_Init   stm32g4xx_hal_tim.o
    0x08004378   0x08004378   0x00000002   Code   RO         3202    i.HAL_TIM_IC_MspInit  stm32g4xx_hal_tim.o
    0x0800437a   0x0800437a   0x00000002   PAD
    0x0800437c   0x0800437c   0x00000278   Code   RO         3204    i.HAL_TIM_IC_Start_DMA  stm32g4xx_hal_tim.o
    0x080045f4   0x080045f4   0x0000009c   Code   RO          404    i.HAL_TIM_MspPostInit  tim.o
    0x08004690   0x08004690   0x00000168   Code   RO         3233    i.HAL_TIM_PWM_ConfigChannel  stm32g4xx_hal_tim.o
    0x080047f8   0x080047f8   0x0000006e   Code   RO         3236    i.HAL_TIM_PWM_Init  stm32g4xx_hal_tim.o
    0x08004866   0x08004866   0x00000002   Code   RO         3238    i.HAL_TIM_PWM_MspInit  stm32g4xx_hal_tim.o
    0x08004868   0x08004868   0x0000014c   Code   RO         3241    i.HAL_TIM_PWM_Start  stm32g4xx_hal_tim.o
    0x080049b4   0x080049b4   0x0000006c   Code   RO         3250    i.HAL_TIM_SlaveConfigSynchro  stm32g4xx_hal_tim.o
    0x08004a20   0x08004a20   0x0000004e   Code   RO         4740    i.HAL_UARTEx_DisableFifoMode  stm32g4xx_hal_uart_ex.o
    0x08004a6e   0x08004a6e   0x00000066   Code   RO         4745    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32g4xx_hal_uart_ex.o
    0x08004ad4   0x08004ad4   0x0000002c   Code   RO         5428    i.HAL_UARTEx_RxEventCallback  uart_app.o
    0x08004b00   0x08004b00   0x00000002   Code   RO         4747    i.HAL_UARTEx_RxFifoFullCallback  stm32g4xx_hal_uart_ex.o
    0x08004b02   0x08004b02   0x0000005e   Code   RO         4748    i.HAL_UARTEx_SetRxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x08004b60   0x08004b60   0x0000005e   Code   RO         4749    i.HAL_UARTEx_SetTxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x08004bbe   0x08004bbe   0x00000002   Code   RO         4751    i.HAL_UARTEx_TxFifoEmptyCallback  stm32g4xx_hal_uart_ex.o
    0x08004bc0   0x08004bc0   0x00000002   Code   RO         4752    i.HAL_UARTEx_WakeupCallback  stm32g4xx_hal_uart_ex.o
    0x08004bc2   0x08004bc2   0x00000002   Code   RO         4340    i.HAL_UART_ErrorCallback  stm32g4xx_hal_uart.o
    0x08004bc4   0x08004bc4   0x00000308   Code   RO         4343    i.HAL_UART_IRQHandler  stm32g4xx_hal_uart.o
    0x08004ecc   0x08004ecc   0x00000078   Code   RO         4344    i.HAL_UART_Init     stm32g4xx_hal_uart.o
    0x08004f44   0x08004f44   0x000000e4   Code   RO          463    i.HAL_UART_MspInit  usart.o
    0x08005028   0x08005028   0x00000002   Code   RO         4351    i.HAL_UART_RxCpltCallback  stm32g4xx_hal_uart.o
    0x0800502a   0x0800502a   0x00000002   Code   RO         4352    i.HAL_UART_RxHalfCpltCallback  stm32g4xx_hal_uart.o
    0x0800502c   0x0800502c   0x000000d6   Code   RO         4353    i.HAL_UART_Transmit  stm32g4xx_hal_uart.o
    0x08005102   0x08005102   0x00000002   Code   RO         4356    i.HAL_UART_TxCpltCallback  stm32g4xx_hal_uart.o
    0x08005104   0x08005104   0x00000004   Code   RO          522    i.HardFault_Handler  stm32g4xx_it.o
    0x08005108   0x08005108   0x00000030   Code   RO         5040    i.LCD_BusIn         lcd.o
    0x08005138   0x08005138   0x00000030   Code   RO         5041    i.LCD_BusOut        lcd.o
    0x08005168   0x08005168   0x00000070   Code   RO         5042    i.LCD_Clear         lcd.o
    0x080051d8   0x080051d8   0x00000094   Code   RO         5044    i.LCD_ClearRegion   lcd.o
    0x0800526c   0x0800526c   0x000000c0   Code   RO         5045    i.LCD_CtrlLinesConfig  lcd.o
    0x0800532c   0x0800532c   0x00000028   Code   RO         5046    i.LCD_DisplayChar   lcd.o
    0x08005354   0x08005354   0x0000002c   Code   RO         5050    i.LCD_DisplayStringLine  lcd.o
    0x08005380   0x08005380   0x000000f8   Code   RO         5051    i.LCD_DrawChar      lcd.o
    0x08005478   0x08005478   0x00000098   Code   RO         5054    i.LCD_DrawLineAny   lcd.o
    0x08005510   0x08005510   0x00000038   Code   RO         5058    i.LCD_Init          lcd.o
    0x08005548   0x08005548   0x000000ac   Code   RO         5061    i.LCD_ReadReg       lcd.o
    0x080055f4   0x080055f4   0x00000010   Code   RO         5062    i.LCD_SetBackColor  lcd.o
    0x08005604   0x08005604   0x00000018   Code   RO         5063    i.LCD_SetCursor     lcd.o
    0x0800561c   0x0800561c   0x00000010   Code   RO         5065    i.LCD_SetTextColor  lcd.o
    0x0800562c   0x0800562c   0x00000048   Code   RO         5066    i.LCD_WR_REG        lcd.o
    0x08005674   0x08005674   0x00000050   Code   RO         5069    i.LCD_WriteRAM      lcd.o
    0x080056c4   0x080056c4   0x0000000a   Code   RO         5070    i.LCD_WriteRAM_Prepare  lcd.o
    0x080056ce   0x080056ce   0x00000002   PAD
    0x080056d0   0x080056d0   0x0000005c   Code   RO         5071    i.LCD_WriteReg      lcd.o
    0x0800572c   0x0800572c   0x00000010   Code   RO          681    i.LL_ADC_Enable     stm32g4xx_hal_adc.o
    0x0800573c   0x0800573c   0x0000000a   Code   RO          682    i.LL_ADC_GetMultimode  stm32g4xx_hal_adc.o
    0x08005746   0x08005746   0x00000012   Code   RO          683    i.LL_ADC_GetOffsetChannel  stm32g4xx_hal_adc.o
    0x08005758   0x08005758   0x0000000a   Code   RO          684    i.LL_ADC_INJ_IsConversionOngoing  stm32g4xx_hal_adc.o
    0x08005762   0x08005762   0x0000000a   Code   RO          686    i.LL_ADC_IsEnabled  stm32g4xx_hal_adc.o
    0x0800576c   0x0800576c   0x0000000a   Code   RO          958    i.LL_ADC_IsEnabled  stm32g4xx_hal_adc_ex.o
    0x08005776   0x08005776   0x0000000a   Code   RO          687    i.LL_ADC_IsInternalRegulatorEnabled  stm32g4xx_hal_adc.o
    0x08005780   0x08005780   0x0000000a   Code   RO          688    i.LL_ADC_REG_IsConversionOngoing  stm32g4xx_hal_adc.o
    0x0800578a   0x0800578a   0x0000000a   Code   RO          959    i.LL_ADC_REG_IsConversionOngoing  stm32g4xx_hal_adc_ex.o
    0x08005794   0x08005794   0x00000012   Code   RO          689    i.LL_ADC_REG_IsTriggerSourceSWStart  stm32g4xx_hal_adc.o
    0x080057a6   0x080057a6   0x00000002   PAD
    0x080057a8   0x080057a8   0x00000010   Code   RO          690    i.LL_ADC_REG_StartConversion  stm32g4xx_hal_adc.o
    0x080057b8   0x080057b8   0x00000028   Code   RO          692    i.LL_ADC_SetChannelSamplingTime  stm32g4xx_hal_adc.o
    0x080057e0   0x080057e0   0x0000000c   Code   RO          693    i.LL_ADC_SetCommonPathInternalCh  stm32g4xx_hal_adc.o
    0x080057ec   0x080057ec   0x00000016   Code   RO          694    i.LL_ADC_SetOffsetState  stm32g4xx_hal_adc.o
    0x08005802   0x08005802   0x0000000c   Code   RO          695    i.LL_ADC_SetSamplingTimeCommonConfig  stm32g4xx_hal_adc.o
    0x0800580e   0x0800580e   0x0000002a   Code   RO         5283    i.LcdSprintf        lcd_app.o
    0x08005838   0x08005838   0x000000a4   Code   RO          289    i.MX_ADC1_Init      adc.o
    0x080058dc   0x080058dc   0x0000008c   Code   RO          290    i.MX_ADC2_Init      adc.o
    0x08005968   0x08005968   0x00000080   Code   RO          336    i.MX_DMA_Init       dma.o
    0x080059e8   0x080059e8   0x00000180   Code   RO          263    i.MX_GPIO_Init      gpio.o
    0x08005b68   0x08005b68   0x0000009c   Code   RO          362    i.MX_RTC_Init       rtc.o
    0x08005c04   0x08005c04   0x000000f0   Code   RO          405    i.MX_TIM1_Init      tim.o
    0x08005cf4   0x08005cf4   0x000000a4   Code   RO          406    i.MX_TIM2_Init      tim.o
    0x08005d98   0x08005d98   0x000000c8   Code   RO          407    i.MX_TIM3_Init      tim.o
    0x08005e60   0x08005e60   0x0000008c   Code   RO          464    i.MX_USART1_UART_Init  usart.o
    0x08005eec   0x08005eec   0x00000004   Code   RO          523    i.MemManage_Handler  stm32g4xx_it.o
    0x08005ef0   0x08005ef0   0x00000004   Code   RO          524    i.NMI_Handler       stm32g4xx_it.o
    0x08005ef4   0x08005ef4   0x00000002   Code   RO          525    i.PendSV_Handler    stm32g4xx_it.o
    0x08005ef6   0x08005ef6   0x00000002   PAD
    0x08005ef8   0x08005ef8   0x00000068   Code   RO         1412    i.RCC_GetSysClockFreqFromPLLSource  stm32g4xx_hal_rcc.o
    0x08005f60   0x08005f60   0x00000120   Code   RO         5072    i.REG_8230_Init     lcd.o
    0x08006080   0x08006080   0x00000200   Code   RO         5073    i.REG_932X_Init     lcd.o
    0x08006280   0x08006280   0x00000014   Code   RO         2725    i.RTC_Bcd2ToByte    stm32g4xx_hal_rtc.o
    0x08006294   0x08006294   0x0000001c   Code   RO         2726    i.RTC_ByteToBcd2    stm32g4xx_hal_rtc.o
    0x080062b0   0x080062b0   0x00000050   Code   RO         2727    i.RTC_EnterInitMode  stm32g4xx_hal_rtc.o
    0x08006300   0x08006300   0x00000068   Code   RO         2728    i.RTC_ExitInitMode  stm32g4xx_hal_rtc.o
    0x08006368   0x08006368   0x00000002   Code   RO          526    i.SVC_Handler       stm32g4xx_it.o
    0x0800636a   0x0800636a   0x00000008   Code   RO          527    i.SysTick_Handler   stm32g4xx_it.o
    0x08006372   0x08006372   0x00000072   Code   RO           14    i.SystemClock_Config  main.o
    0x080063e4   0x080063e4   0x00000014   Code   RO         4858    i.SystemInit        system_stm32g4xx.o
    0x080063f8   0x080063f8   0x000000bc   Code   RO         3254    i.TIM_Base_SetConfig  stm32g4xx_hal_tim.o
    0x080064b4   0x080064b4   0x00000022   Code   RO         3255    i.TIM_CCxChannelCmd  stm32g4xx_hal_tim.o
    0x080064d6   0x080064d6   0x0000007a   Code   RO         3256    i.TIM_DMACaptureCplt  stm32g4xx_hal_tim.o
    0x08006550   0x08006550   0x00000040   Code   RO         3257    i.TIM_DMACaptureHalfCplt  stm32g4xx_hal_tim.o
    0x08006590   0x08006590   0x0000005e   Code   RO         3260    i.TIM_DMAError      stm32g4xx_hal_tim.o
    0x080065ee   0x080065ee   0x00000016   Code   RO         3265    i.TIM_ETR_SetConfig  stm32g4xx_hal_tim.o
    0x08006604   0x08006604   0x00000018   Code   RO         3266    i.TIM_ITRx_SetConfig  stm32g4xx_hal_tim.o
    0x0800661c   0x0800661c   0x000000ac   Code   RO         3267    i.TIM_OC1_SetConfig  stm32g4xx_hal_tim.o
    0x080066c8   0x080066c8   0x000000b4   Code   RO         3268    i.TIM_OC2_SetConfig  stm32g4xx_hal_tim.o
    0x0800677c   0x0800677c   0x000000b4   Code   RO         3269    i.TIM_OC3_SetConfig  stm32g4xx_hal_tim.o
    0x08006830   0x08006830   0x000000b4   Code   RO         3270    i.TIM_OC4_SetConfig  stm32g4xx_hal_tim.o
    0x080068e4   0x080068e4   0x00000070   Code   RO         3271    i.TIM_OC5_SetConfig  stm32g4xx_hal_tim.o
    0x08006954   0x08006954   0x00000070   Code   RO         3272    i.TIM_OC6_SetConfig  stm32g4xx_hal_tim.o
    0x080069c4   0x080069c4   0x000000fc   Code   RO         3273    i.TIM_SlaveTimer_SetConfig  stm32g4xx_hal_tim.o
    0x08006ac0   0x08006ac0   0x00000026   Code   RO         3274    i.TIM_TI1_ConfigInputStage  stm32g4xx_hal_tim.o
    0x08006ae6   0x08006ae6   0x00000002   PAD
    0x08006ae8   0x08006ae8   0x0000007c   Code   RO         3275    i.TIM_TI1_SetConfig  stm32g4xx_hal_tim.o
    0x08006b64   0x08006b64   0x00000028   Code   RO         3276    i.TIM_TI2_ConfigInputStage  stm32g4xx_hal_tim.o
    0x08006b8c   0x08006b8c   0x0000003a   Code   RO         3277    i.TIM_TI2_SetConfig  stm32g4xx_hal_tim.o
    0x08006bc6   0x08006bc6   0x00000038   Code   RO         3278    i.TIM_TI3_SetConfig  stm32g4xx_hal_tim.o
    0x08006bfe   0x08006bfe   0x0000003c   Code   RO         3279    i.TIM_TI4_SetConfig  stm32g4xx_hal_tim.o
    0x08006c3a   0x08006c3a   0x00000002   PAD
    0x08006c3c   0x08006c3c   0x00000058   Code   RO         4753    i.UARTEx_SetNbDataToProcess  stm32g4xx_hal_uart_ex.o
    0x08006c94   0x08006c94   0x000000f8   Code   RO         4358    i.UART_AdvFeatureConfig  stm32g4xx_hal_uart.o
    0x08006d8c   0x08006d8c   0x00000076   Code   RO         4359    i.UART_CheckIdleState  stm32g4xx_hal_uart.o
    0x08006e02   0x08006e02   0x00000018   Code   RO         4360    i.UART_DMAAbortOnError  stm32g4xx_hal_uart.o
    0x08006e1a   0x08006e1a   0x0000005e   Code   RO         4361    i.UART_DMAError     stm32g4xx_hal_uart.o
    0x08006e78   0x08006e78   0x0000006c   Code   RO         4362    i.UART_DMAReceiveCplt  stm32g4xx_hal_uart.o
    0x08006ee4   0x08006ee4   0x00000022   Code   RO         4364    i.UART_DMARxHalfCplt  stm32g4xx_hal_uart.o
    0x08006f06   0x08006f06   0x00000002   PAD
    0x08006f08   0x08006f08   0x0000003c   Code   RO         4370    i.UART_EndRxTransfer  stm32g4xx_hal_uart.o
    0x08006f44   0x08006f44   0x00000022   Code   RO         4371    i.UART_EndTransmit_IT  stm32g4xx_hal_uart.o
    0x08006f66   0x08006f66   0x00000020   Code   RO         4372    i.UART_EndTxTransfer  stm32g4xx_hal_uart.o
    0x08006f86   0x08006f86   0x00000002   PAD
    0x08006f88   0x08006f88   0x0000038c   Code   RO         4377    i.UART_SetConfig    stm32g4xx_hal_uart.o
    0x08007314   0x08007314   0x000000a0   Code   RO         4378    i.UART_Start_Receive_DMA  stm32g4xx_hal_uart.o
    0x080073b4   0x080073b4   0x000000be   Code   RO         4384    i.UART_WaitOnFlagUntilTimeout  stm32g4xx_hal_uart.o
    0x08007472   0x08007472   0x00000002   PAD
    0x08007474   0x08007474   0x00000030   Code   RO          528    i.USART1_IRQHandler  stm32g4xx_it.o
    0x080074a4   0x080074a4   0x00000004   Code   RO          529    i.UsageFault_Handler  stm32g4xx_it.o
    0x080074a8   0x080074a8   0x00000020   Code   RO         6256    i.__0printf         mc_w.l(printfa.o)
    0x080074c8   0x080074c8   0x00000024   Code   RO         6262    i.__0vsprintf       mc_w.l(printfa.o)
    0x080074ec   0x080074ec   0x00000010   Code   RO         2581    i.__NVIC_GetPriorityGrouping  stm32g4xx_hal_cortex.o
    0x080074fc   0x080074fc   0x00000028   Code   RO         2582    i.__NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x08007524   0x08007524   0x0000003a   Code   RO         5998    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x0800755e   0x0800755e   0x0000000e   Code   RO         6363    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800756c   0x0800756c   0x00000002   Code   RO         6364    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800756e   0x0800756e   0x0000000e   Code   RO         6365    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800757c   0x0800757c   0x0000000c   Code   RO         6339    i.__set_errno       mc_w.l(errno.o)
    0x08007588   0x08007588   0x00000184   Code   RO         6263    i._fp_digits        mc_w.l(printfa.o)
    0x0800770c   0x0800770c   0x000006dc   Code   RO         6264    i._printf_core      mc_w.l(printfa.o)
    0x08007de8   0x08007de8   0x00000024   Code   RO         6265    i._printf_post_padding  mc_w.l(printfa.o)
    0x08007e0c   0x08007e0c   0x0000002e   Code   RO         6266    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08007e3a   0x08007e3a   0x0000000a   Code   RO         6268    i._sputc            mc_w.l(printfa.o)
    0x08007e44   0x08007e44   0x00000134   Code   RO         5512    i.adc_proc          adc_app.o
    0x08007f78   0x08007f78   0x00000048   Code   RO         5284    i.calculate_duty_cycle  lcd_app.o
    0x08007fc0   0x08007fc0   0x00000034   Code   RO         5285    i.cusum_detect      lcd_app.o
    0x08007ff4   0x08007ff4   0x00000034   Code   RO         5286    i.cusum_detect_duty  lcd_app.o
    0x08008028   0x08008028   0x0000006c   Code   RO         5797    i.fft_process       fft_app.o
    0x08008094   0x08008094   0x00000060   Code   RO         5798    i.find_peak_frequency  fft_app.o
    0x080080f4   0x080080f4   0x00000018   Code   RO          465    i.fputc             usart.o
    0x0800810c   0x0800810c   0x00000068   Code   RO         5753    i.ic_proc           tim_app.o
    0x08008174   0x08008174   0x0000005c   Code   RO         4987    i.key_init          key_app.o
    0x080081d0   0x080081d0   0x000000b4   Code   RO         4988    i.key_proc          key_app.o
    0x08008284   0x08008284   0x00000040   Code   RO         4989    i.key_read          key_app.o
    0x080082c4   0x080082c4   0x000003ec   Code   RO         5287    i.lcd_proc          lcd_app.o
    0x080086b0   0x080086b0   0x0000006c   Code   RO         5367    i.led_disp          led_app.o
    0x0800871c   0x0800871c   0x00000010   Code   RO         5368    i.led_proc          led_app.o
    0x0800872c   0x0800872c   0x0000002a   Code   RO         5547    i.limit_value       filter.o
    0x08008756   0x08008756   0x00000002   PAD
    0x08008758   0x08008758   0x000000a4   Code   RO           15    i.main              main.o
    0x080087fc   0x080087fc   0x00000016   Code   RO         5288    i.map               lcd_app.o
    0x08008812   0x08008812   0x0000004c   Code   RO         5074    i.my_LCD_DrawLine   lcd.o
    0x0800885e   0x0800885e   0x0000001a   Code   RO         5464    i.ringbuffer_init   ringbuffer.o
    0x08008878   0x08008878   0x0000000e   Code   RO         5465    i.ringbuffer_is_empty  ringbuffer.o
    0x08008886   0x08008886   0x00000010   Code   RO         5466    i.ringbuffer_is_full  ringbuffer.o
    0x08008896   0x08008896   0x00000044   Code   RO         5467    i.ringbuffer_read   ringbuffer.o
    0x080088da   0x080088da   0x00000044   Code   RO         5468    i.ringbuffer_write  ringbuffer.o
    0x0800891e   0x0800891e   0x00000002   PAD
    0x08008920   0x08008920   0x00000024   Code   RO         5721    i.rtc_proc          rtc_app.o
    0x08008944   0x08008944   0x0000000c   Code   RO         4894    i.scheduler_init    scheduler.o
    0x08008950   0x08008950   0x00000058   Code   RO         4895    i.scheduler_run     scheduler.o
    0x080089a8   0x080089a8   0x0000003c   Code   RO         5404    i.system_init       system.o
    0x080089e4   0x080089e4   0x0000004c   Code   RO         5429    i.uart_proc         uart_app.o
    0x08008a30   0x08008a30   0x00000018   Data   RO         4385    .constdata          stm32g4xx_hal_uart.o
    0x08008a48   0x08008a48   0x00000010   Data   RO         4755    .constdata          stm32g4xx_hal_uart_ex.o
    0x08008a58   0x08008a58   0x00000018   Data   RO         4859    .constdata          system_stm32g4xx.o
    0x08008a70   0x08008a70   0x000011d0   Data   RO         5075    .constdata          lcd.o
    0x08009c40   0x08009c40   0x00000800   Data   RO         5879    .rodata.armBitRevTable  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800a440   0x0800a440   0x00008000   Data   RO         5897    .rodata.twiddleCoef_4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08012440   0x08012440   0x00000020   Data   RO         6361    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08012460, Size: 0x00007bb8, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08012460   0x00000004   Data   RW          292    .data               adc.o
    0x20000004   0x08012464   0x0000000c   Data   RW         1198    .data               stm32g4xx_hal.o
    0x20000010   0x08012470   0x00000004   Data   RW         4860    .data               system_stm32g4xx.o
    0x20000014   0x08012474   0x00000058   Data   RW         4896    .data               scheduler.o
    0x2000006c   0x080124cc   0x00000004   Data   RW         4993    .data               key_app.o
    0x20000070   0x080124d0   0x00000006   Data   RW         5076    .data               lcd.o
    0x20000076   0x080124d6   0x00000002   PAD
    0x20000078   0x080124d8   0x0000002c   Data   RW         5290    .data               lcd_app.o
    0x200000a4   0x08012504   0x00000009   Data   RW         5369    .data               led_app.o
    0x200000ad   0x0801250d   0x00000003   PAD
    0x200000b0   0x08012510   0x00000025   Data   RW         5514    .data               adc_app.o
    0x200000d5   0x08012535   0x00000004   Data   RW         5723    .data               rtc_app.o
    0x200000d9   0x08012539   0x00000003   PAD
    0x200000dc   0x0801253c   0x00000008   Data   RW         5757    .data               tim_app.o
    0x200000e4   0x08012544   0x00000004   Data   RW         6328    .data               mc_w.l(stdout.o)
    0x200000e8   0x08012548   0x00000004   Data   RW         6340    .data               mc_w.l(errno.o)
    0x200000ec        -       0x00000198   Zero   RW          291    .bss                adc.o
    0x20000284        -       0x00000028   Zero   RW          363    .bss                rtc.o
    0x200002ac        -       0x00000144   Zero   RW          408    .bss                tim.o
    0x200003f0        -       0x000001f0   Zero   RW          466    .bss                usart.o
    0x200005e0        -       0x00000040   Zero   RW         4992    .bss                key_app.o
    0x20000620        -       0x00001000   Zero   RW         5289    .bss                lcd_app.o
    0x20001620        -       0x0000006c   Zero   RW         5430    .bss                uart_app.o
    0x2000168c        -       0x00003000   Zero   RW         5513    .bss                adc_app.o
    0x2000468c        -       0x00000014   Zero   RW         5722    .bss                rtc_app.o
    0x200046a0        -       0x00000100   Zero   RW         5756    .bss                tim_app.o
    0x200047a0        -       0x00003014   Zero   RW         5799    .bss                fft_app.o
    0x200077b4   0x0801254c   0x00000004   PAD
    0x200077b8        -       0x00000400   Zero   RW            1    STACK               startup_stm32g431xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       756         48          0          4        408       2842   adc.o
       308         58          0         37      12288       1664   adc_app.o
       128          6          0          0          0        810   dma.o
       204         32          0          0      12308       1847   fft_app.o
        42          0          0          0          0       1074   filter.o
       384         18          0          0          0       1095   gpio.o
       336         54          0          4         64       2272   key_app.o
      2476        112       4560          6          0      14071   lcd.o
      2004        260          0         44       4096       9347   lcd_app.o
       124         20          0          9          0       1562   led_app.o
       284         30          0          0          0     698557   main.o
       192          0          0          0          0       3278   ringbuffer.o
       256         20          0          0         40       1727   rtc.o
        36         12          0          4         20        675   rtc_app.o
       100         14          0         88          0       4447   scheduler.o
        36          8        472          0       1024        832   startup_stm32g431xx.o
       194         32          0         12          0       4281   stm32g4xx_hal.o
      3530        122          0          0          0      84573   stm32g4xx_hal_adc.o
       298          8          0          0          0      70776   stm32g4xx_hal_adc_ex.o
       310         22          0          0          0      34331   stm32g4xx_hal_cortex.o
      1090         22          0          0          0       6254   stm32g4xx_hal_dma.o
       498         34          0          0          0       2915   stm32g4xx_hal_gpio.o
        68          6          0          0          0        846   stm32g4xx_hal_msp.o
       300         18          0          0          0       1397   stm32g4xx_hal_pwr_ex.o
      2272        108          0          0          0       6916   stm32g4xx_hal_rcc.o
       880          8          0          0          0       1864   stm32g4xx_hal_rcc_ex.o
      1124         60          0          0          0       7901   stm32g4xx_hal_rtc.o
      4400        306          0          0          0      26071   stm32g4xx_hal_tim.o
       416         34          0          0          0       2268   stm32g4xx_hal_tim_ex.o
      3128         94         24          0          0      33820   stm32g4xx_hal_uart.o
       462         10         16          0          0       6253   stm32g4xx_hal_uart_ex.o
       170         44          0          0          0       7174   stm32g4xx_it.o
        60         10          0          0          0        446   system.o
        20          6         24          4          0       1115   system_stm32g4xx.o
      1024         62          0          0        324       4324   tim.o
       104         16          0          8        256       1093   tim_app.o
       120         44          0          0        108       1974   uart_app.o
       392         38          0          0        496       2739   usart.o

    ----------------------------------------------------------------------
     28564       <USER>       <GROUP>        228      31436    1055431   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        38          0          0          8          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       190          0          0          0          0       2756   arm_bitreversal.o
      1812          0          0          0          0       9774   arm_cfft_radix4_f32.o
       148         20          0          0          0       1573   arm_cfft_radix4_init_f32.o
       340          0          0          0          0       2270   arm_cmplx_mag_f32.o
         0          0      34816          0          0       6294   arm_common_tables.o
        58          0          0          0          0        136   sqrtf.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2304         96          0          0          0        600   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o

    ----------------------------------------------------------------------
      6340        <USER>      <GROUP>          8          0      24747   Library Totals
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2490         20      34816          0          0      22667   arm_cortexM4lf_math.lib
        58          0          0          0          0        136   m_wm.l
      2678        118          0          8          0       1220   mc_w.l
      1104          0          0          0          0        724   mf_w.l

    ----------------------------------------------------------------------
      6340        <USER>      <GROUP>          8          0      24747   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     34904       1934      39944        236      31436    1057698   Grand Totals
     34904       1934      39944        236      31436    1057698   ELF Image Totals
     34904       1934      39944        236          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                74848 (  73.09kB)
    Total RW  Size (RW Data + ZI Data)             31672 (  30.93kB)
    Total ROM Size (Code + RO Data + RW Data)      75084 (  73.32kB)

==============================================================================

