# LCD坐标系统和重影问题分析

## LCD物理布局
- **物理尺寸**: 240×320像素
- **坐标原点**: 左上角(0,0)
- **X轴范围**: 0-239
- **Y轴范围**: 0-319

## 屏幕区域分配

### 文字显示区域
- **X坐标范围**: 0-216像素
- **显示内容**: 
  - Line0 (X=0): Vpp值
  - Line1 (X=24): Vmax值  
  - Line2 (X=48): Vmin值
  - Line3 (X=72): 占空比
  - Line4 (X=96): 频率
  - Line5-Line9 (X=120-216): 预留

### 波形显示区域
- **清除区域**: LCD_ClearRegion(125,0,240,320,Black)
- **实际显示范围**: X=125-239, Y=0-319
- **区域宽度**: 115像素
- **区域高度**: 320像素

## 坐标变换分析

### 波形数据映射
```c
// ADC数据 -> 屏幕坐标映射
int x1 = map(data[i], 0, 4095, 0, SCREEN_HEIGHT);
// SCREEN_HEIGHT=115时，映射范围是0-115
```

### 镜像变换 (my_LCD_DrawLine函数)
```c
u16 MirrorX1 = Width - 1 - X1;   // 240-1-X1 = 239-X1
u16 MirrorY1 = Height - 1 - Y1;  // 320-1-Y1 = 319-Y1
```

### 最终显示位置计算
- **映射前X范围**: 0-115
- **镜像后X范围**: 239-115=124 到 239-0=239
- **实际显示区域**: X=124-239 (与清除区域125-239基本匹配)

## 重影问题分析

### 问题原因
当SCREEN_HEIGHT过大时：
1. 波形映射范围扩大
2. 镜像后的显示区域向左扩展
3. 与文字显示区域(0-216)产生重叠

### 解决方案
设置SCREEN_HEIGHT=115：
- 镜像后范围：124-239
- 文字显示范围：0-216  
- 清除区域：125-239
- **无重叠区域**，完美分离

## 显示效果预期
- ✅ 文字信息显示在左侧(0-216)
- ✅ 波形显示在右侧(124-239)  
- ✅ 中间有小间隙(217-123)避免重叠
- ✅ 波形有足够的显示范围避免上部横线
- ✅ 无重影问题

## 关键参数总结
- **SCREEN_HEIGHT**: 115 (优化值)
- **文字区域**: X=0-216
- **波形区域**: X=124-239 (镜像后)
- **清除区域**: X=125-239
- **分离间隙**: X=217-123 (约6像素)
