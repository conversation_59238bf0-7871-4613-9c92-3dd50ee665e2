# 信号测量校准系统使用指南

## 概述
本校准系统为您的示波器提供了简单直接的测量精度校准功能，无需复杂的按键操作。

## 校准原理
校准系统使用线性校准算法：
- **校准后的值 = 原始值 × 增益系数 + 偏移量**

## 当前校准参数

### 占空比校准
- **增益系数**: 1.02 (增加2%)
- **偏移量**: -0.5% (减少0.5%)
- **效果**: 如果原始测量为50%，校准后为: 50 × 1.02 - 0.5 = 50.5%

### 频率校准  
- **增益系数**: 0.998 (减少0.2%)
- **偏移量**: +10Hz (增加10Hz)
- **效果**: 如果原始测量为1000Hz，校准后为: 1000 × 0.998 + 10 = 1008Hz

## 如何调整校准参数

### 方法1: 修改代码中的参数
在 `APP/calibration.c` 文件的 `calibration_init()` 函数中修改：

```c
void calibration_init(void)
{
    // 占空比校准参数 - 根据您的测试结果调整
    g_calib_params.duty_gain = 1.02f;      // 占空比增益校准
    g_calib_params.duty_offset = -0.5f;    // 占空比偏移校准
    
    // 频率校准参数 - 根据您的测试结果调整  
    g_calib_params.freq_gain = 0.998f;     // 频率增益校准
    g_calib_params.freq_offset = 10.0f;    // 频率偏移校准
}
```

### 方法2: 运行时调整
在代码中任何地方调用以下函数：

```c
// 设置占空比校准参数
set_duty_calibration(1.05f, -1.0f);  // 增益1.05，偏移-1.0%

// 设置频率校准参数  
set_freq_calibration(0.995f, 20.0f); // 增益0.995，偏移+20Hz
```

## 校准步骤建议

### 1. 占空比校准
1. **准备标准信号**: 使用信号发生器产生已知占空比的方波信号（如50%）
2. **连接测试**: 将信号连接到示波器输入
3. **记录误差**: 观察显示的占空比与实际值的差异
4. **计算校准参数**:
   - 如果显示49%，实际50%，则增益 = 50/49 ≈ 1.02
   - 如果显示50.5%，实际50%，则偏移 = 50 - 50.5 = -0.5

### 2. 频率校准
1. **准备标准信号**: 使用信号发生器产生已知频率的信号（如1000Hz）
2. **连接测试**: 将信号连接到示波器输入
3. **记录误差**: 观察显示的频率与实际值的差异
4. **计算校准参数**:
   - 如果显示1002Hz，实际1000Hz，则增益 = 1000/1002 ≈ 0.998
   - 如果显示990Hz，实际1000Hz，则偏移 = 1000 - 990 = +10

## 校准参数范围建议

### 安全范围
- **增益系数**: 0.8 - 1.2 (±20%)
- **占空比偏移**: -5% 到 +5%
- **频率偏移**: -100Hz 到 +100Hz

### 注意事项
1. **小幅调整**: 建议每次调整幅度不超过5%
2. **多点验证**: 使用不同频率和占空比的信号验证校准效果
3. **记录参数**: 记录每次调整的参数和效果，便于回退
4. **重新编译**: 修改代码后需要重新编译和烧录固件

## 校准效果验证

### 验证方法
1. 使用精确的信号发生器产生标准信号
2. 对比校准前后的测量误差
3. 在不同频率范围内验证校准效果
4. 记录校准改善的精度数据

### 预期效果
- **占空比精度**: 误差应小于±1%
- **频率精度**: 误差应小于±0.5%
- **稳定性**: 校准参数应在不同温度和时间下保持稳定

## 故障排除

### 校准无效果
- 检查校准参数是否正确设置
- 确认 `calibration_init()` 函数被正确调用
- 验证测量算法是否正确应用校准

### 校准过度
- 减小增益系数的调整幅度
- 检查偏移量是否设置过大
- 使用多个标准信号验证校准效果

### 系统不稳定
- 检查校准参数是否在合理范围内
- 确认没有引入数值溢出或下溢
- 验证校准算法的数值稳定性

## 技术支持
如需进一步的校准支持或遇到问题，请联系技术支持团队。
